'use strict';self.dispatchPort=null;self.outputPort=null;self.workerNumber=-1;self.activeJobId=null;self.sentBlobs=new Map;self.sentBuffers=new Map;self.JobHandlers={};
function FlipImageData(data,width,height){const stride=width*4;const tempRow=new Uint8Array(stride);const imageBuffer=data.buffer;for(let topY=0,len=Math.floor(height/2);topY<len;++topY){const bottomY=height-topY-1;const topRow=new Uint8Array(imageBuffer,topY*stride,stride);const bottomRow=new Uint8Array(imageBuffer,bottomY*stride,stride);tempRow.set(topRow);topRow.set(bottomRow);bottomRow.set(tempRow)}}
function UnpremultiplyImageData(data){for(let ptr=0,len=data.length;ptr<len;ptr+=4){const a=data[ptr+3];if(a===255)continue;const scale=255/a;data[ptr]*=scale;data[ptr+1]*=scale;data[ptr+2]*=scale}}
self.JobHandlers["ProcessImageData"]=function(params){const buffer=params["buffer"];const data=new Uint8Array(buffer);const width=params["width"];const height=params["height"];if(params["flipY"])FlipImageData(data,width,height);if(params["unpremultiply"])UnpremultiplyImageData(data);return{result:buffer,transferables:[buffer]}};
self.addEventListener("message",e=>{const msg=e.data;const type=msg["type"];switch(type){case "init":self.workerNumber=msg["number"];self.dispatchPort=msg["dispatch-port"];self.dispatchPort.onmessage=OnDispatchWorkerMessage;self.outputPort=msg["output-port"];return;case "terminate":self.close();return;default:console.error("unknown message '"+type+"'");return}});function SendReady(){self.dispatchPort.postMessage({"type":"ready"});self.outputPort.postMessage({"type":"ready"})}
function SendError(isBroadcast,e){if(!isBroadcast)self.outputPort.postMessage({"type":"error","jobId":self.activeJobId,"error":e.toString()});SendDone()}function SendResult(isBroadcast,ret){if(!isBroadcast){const transferables=ret.transferables||[];self.outputPort.postMessage({"type":"result","jobId":self.activeJobId,"result":ret.result},transferables)}SendDone()}function SendDone(){self.activeJobId=null;self.dispatchPort.postMessage({"type":"done"})}
function SendProgress(val){self.outputPort.postMessage({"type":"progress","jobId":self.activeJobId,"progress":val})}
function OnDispatchWorkerMessage(e){const msg=e.data;const type=msg["type"];if(type==="_import_scripts"){importScripts(...msg["scripts"]);return}else if(type==="_send_blob"){self.sentBlobs.set(msg["id"],msg["blob"]);return}else if(type==="_send_buffer"){self.sentBuffers.set(msg["id"],msg["buffer"]);return}else if(type==="_ready"){SendReady();return}const jobId=msg["jobId"];const isBroadcast=msg["isBroadcast"];const params=msg["params"];let ret;self.activeJobId=jobId;if(!self.JobHandlers.hasOwnProperty(type)){console.error(`no handler for message type '${type}'`);
return}try{ret=self.JobHandlers[type](params)}catch(e){SendError(isBroadcast,"Exception in job handler: "+e);return}if(ret&&ret.then)ret.then(asyncRet=>SendResult(isBroadcast,asyncRet)).catch(err=>SendError(isBroadcast,"Rejection in job handler: "+err));else SendResult(isBroadcast,ret)};
