{"skeleton": {"hash": "q81tdigU4Ac", "spine": "4.1.23", "x": -1437.67, "y": -445.86, "width": 2978.16, "height": 1543.86, "images": "./ima/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -760.29, "y": -600.89}, {"name": "bone2", "parent": "bone", "x": 110.5, "y": 512.58}, {"name": "bone3", "parent": "bone2", "length": 261.68, "rotation": 75.16, "x": 9.91, "y": 15.13}, {"name": "bone4", "parent": "bone3", "length": 339.99, "rotation": 11.56, "x": 261.68}, {"name": "bone5", "parent": "bone2", "length": 63.11, "rotation": -61.14, "x": 15.77, "y": -40.34}, {"name": "bone10", "parent": "bone2", "length": 69.68, "rotation": -150.95, "x": -86.88, "y": -14.39}, {"name": "bone15", "parent": "bone3", "length": 184.21, "rotation": 144.08, "x": 187.13, "y": 98.82}, {"name": "bone16", "parent": "bone15", "length": 160.95, "rotation": -6.6, "x": 184.21}, {"name": "bone17", "parent": "bone3", "length": 158.94, "rotation": -106.8, "x": 218.05, "y": -82.65}, {"name": "bone18", "parent": "bone17", "length": 164.77, "rotation": 141.9, "x": 169.88, "y": 2.64}, {"name": "bone19", "parent": "bone18", "length": 74.99, "rotation": -45.61, "x": 164.77}, {"name": "bone20", "parent": "bone3", "length": 40.96, "rotation": 22.08, "x": 186.09, "y": -70.99}, {"name": "bone21", "parent": "bone20", "length": 33.55, "rotation": 144.96, "x": 29.83, "y": 35.22}, {"name": "bone23", "parent": "bone21", "length": 36.86, "rotation": 30.59, "x": 58.61, "y": 12.99}, {"name": "bone25", "parent": "bone23", "length": 27.7, "rotation": -9.74, "x": 61.31, "y": -1.72}, {"name": "bone26", "parent": "bone20", "length": 41.51, "rotation": -154.29, "x": 20.68, "y": -21.48}, {"name": "bone28", "parent": "bone26", "length": 28.45, "rotation": 0.02, "x": 69.44, "y": -1.96}, {"name": "bone30", "parent": "bone28", "length": 19.18, "rotation": 9.3, "x": 52.56, "y": 2.81}, {"name": "bone31", "parent": "bone17", "length": 257.94, "rotation": -39.57, "x": 106.58, "y": 14.79}, {"name": "bone32", "parent": "bone31", "length": 48.53, "rotation": 84.65, "x": 49.08, "y": 13.64, "color": "000000ff"}, {"name": "bone34", "parent": "bone32", "length": 34.49, "rotation": 9.3, "x": 82.8, "y": 6.57, "color": "000000ff"}, {"name": "bone36", "parent": "bone31", "length": 44.51, "rotation": 7.18, "x": 77.64, "y": 14.76, "color": "000000ff"}, {"name": "bone38", "parent": "bone36", "length": 24.82, "rotation": -18.85, "x": 74.83, "y": -3.36, "color": "000000ff"}, {"name": "bone41", "parent": "bone17", "length": 35.62, "rotation": -136.73, "x": 155.97, "y": -84.93, "color": "000000ff"}, {"name": "bone43", "parent": "bone41", "length": 28.3, "rotation": 31.9, "x": 59.59, "y": 4.49, "color": "000000ff"}, {"name": "bone44", "parent": "bone3", "length": 69.11, "rotation": -93.33, "x": 196.48, "y": -174.35, "color": "26c6a8ff"}, {"name": "bone46", "parent": "bone44", "length": 56.35, "rotation": 28.66, "x": 121.4, "y": 11.76, "color": "26c6a8ff"}, {"name": "bone48", "parent": "bone46", "length": 41.27, "rotation": 44.63, "x": 93.41, "y": 25.48, "color": "26c6a8ff"}, {"name": "bone50", "parent": "bone4", "x": 434.78, "y": 64.41}, {"name": "bone51", "parent": "bone50", "length": 46.09, "rotation": 94.27, "x": -5.84, "y": 21.53}, {"name": "bone53", "parent": "bone51", "length": 34.55, "rotation": -14.97, "x": 87.25, "y": -4.24}, {"name": "bone54", "parent": "bone50", "length": 39.89, "rotation": -75.09, "x": 7.05, "y": -22.61}, {"name": "bone56", "parent": "bone54", "length": 33.93, "rotation": -39.93, "x": 79.04, "y": -9.23}, {"name": "bone57", "parent": "bone4", "length": 36.33, "rotation": -31.41, "x": 402.3, "y": 48.73}, {"name": "bone59", "parent": "bone57", "length": 35.02, "rotation": 72.72, "x": 65.8, "y": 22.26}, {"name": "bone61", "parent": "bone59", "length": 31.11, "rotation": 55.59, "x": 66.96, "y": 14.18}, {"name": "bone62", "parent": "bone4", "length": 84.85, "rotation": -115.85, "x": 399.11, "y": -3.9}, {"name": "bone64", "parent": "bone62", "length": 74.68, "rotation": -38.89, "x": 157.23, "y": -25.04}, {"name": "bone66", "parent": "bone64", "length": 59.98, "rotation": -25.02, "x": 153.16, "y": -13.32}, {"name": "bone68", "parent": "bone4", "length": 85.91, "rotation": 154.54, "x": 368.97, "y": 118.5}, {"name": "bone70", "parent": "bone68", "length": 72.92, "rotation": 39.3, "x": 168.36, "y": 34.12}, {"name": "bone72", "parent": "bone70", "length": 66.2, "rotation": 36.67, "x": 136.04, "y": 20.37}, {"name": "bone73", "parent": "bone4", "length": 70.09, "rotation": 126.97, "x": 399.64, "y": 114.18, "color": "16d194ff"}, {"name": "bone75", "parent": "bone73", "length": 86.09, "rotation": 47.38, "x": 132.77, "y": 37.07, "color": "16d194ff"}, {"name": "bone77", "parent": "bone75", "length": 48.42, "rotation": 26.46, "x": 152.18, "y": 17.76, "color": "16d194ff"}, {"name": "bone79", "parent": "bone4", "length": 36.69, "rotation": 144.62, "x": 270.49, "y": 230.42}, {"name": "bone83", "parent": "bone4", "length": 51.18, "rotation": -177.8, "x": 167.74, "y": -161.74}, {"name": "bone87", "parent": "bone3", "length": 77, "rotation": 133.45, "x": 229.1, "y": 154}, {"name": "bone89", "parent": "bone87", "length": 53.66, "rotation": -8.52, "x": 149.48, "y": 12.26}, {"name": "bone92", "parent": "bone3", "length": 65.32, "rotation": 153.65, "x": 153.85, "y": 78.12}, {"name": "bone95", "parent": "bone4", "length": 47.1, "rotation": 176.65, "x": 386.54, "y": 92.37, "color": "020202ff"}, {"name": "bone97", "parent": "bone95", "length": 46.78, "rotation": 6.63, "x": 92.87, "y": 2.04, "color": "020202ff"}, {"name": "bone99", "parent": "bone4", "length": 42.56, "rotation": -119.19, "x": 400.45, "y": 39.77, "color": "020202ff"}, {"name": "bone100", "parent": "bone99", "length": 49.83, "rotation": -21.41, "x": 42.56, "color": "020202ff"}, {"name": "bone102", "parent": "bone100", "length": 51, "rotation": -25.05, "x": 94.85, "y": -13.99, "color": "020202ff"}, {"name": "bone104", "parent": "bone102", "length": 44.61, "rotation": -23.75, "x": 93.92, "y": -7.29, "color": "020202ff"}, {"name": "bone105", "parent": "bone97", "length": 38.1, "rotation": 1.64, "x": 88.13, "y": 1.09, "color": "020202ff"}, {"name": "bone106", "parent": "bone4", "length": 28.28, "rotation": 3.28, "x": 37.85, "y": -36.01}, {"name": "bone107", "parent": "bone3", "length": 70.52, "rotation": 92.75, "x": 213.66, "y": -21.8}, {"name": "bone109", "parent": "bone107", "length": 65.33, "rotation": -16.52, "x": 131.12, "y": -24.28}, {"name": "bone111", "parent": "bone109", "length": 63.42, "rotation": 22.37, "x": 125.39, "y": 6.68}, {"name": "bone121", "parent": "bone3", "length": 28.93, "rotation": -48.22, "x": 231.57, "y": -77.63}, {"name": "bone123", "parent": "bone121", "length": 28.53, "rotation": 48.28, "x": 50.56, "y": 11.84}, {"name": "bone124", "parent": "bone121", "length": 35.05, "rotation": -54.77, "x": 18.79, "y": -12.45}, {"name": "bone126", "parent": "bone31", "length": 93.57, "rotation": 149.56, "x": 78.12, "y": -102.59}, {"name": "bone127", "parent": "bone64", "length": 44.61, "rotation": 32.63, "x": 26.61, "y": 43.72}, {"name": "bone131", "parent": "bone4", "length": 40.65, "rotation": -141.32, "x": 361.78, "y": -43.29, "color": "1bef3aff"}, {"name": "bone136", "parent": "bone3", "length": 53.43, "rotation": -161.21, "x": 217.31, "y": -66.06, "color": "256dd0ff"}, {"name": "bone138", "parent": "bone136", "length": 34.31, "rotation": -11.64, "x": 85.26, "y": -5.88, "color": "256dd0ff"}, {"name": "bone140", "parent": "bone138", "length": 27.42, "rotation": -5.87, "x": 60.43, "y": -1.11, "color": "256dd0ff"}, {"name": "bone141", "parent": "bone3", "length": 39.61, "rotation": -170.59, "x": 211.15, "y": 7.42, "color": "256dd0ff"}, {"name": "bone143", "parent": "bone141", "length": 33.13, "rotation": 0.81, "x": 77.26, "y": 1.71, "color": "256dd0ff"}, {"name": "bone145", "parent": "bone143", "length": 22.98, "rotation": -13.08, "x": 64.54, "y": -1.94, "color": "256dd0ff"}, {"name": "bone146", "parent": "bone16", "length": 49.32, "rotation": -83.41, "x": 176.91, "y": 11.22}, {"name": "bone147", "parent": "bone31", "length": 58, "rotation": 128.84, "x": 127.3, "y": 156.88}, {"name": "bone148", "parent": "bone31", "length": 47.03, "rotation": 175.65, "x": 111.93, "y": 141.7}, {"name": "bone149", "parent": "bone31", "length": 40.59, "rotation": -50.59, "x": 161.55, "y": 120.44}, {"name": "bone150", "parent": "root", "x": 751.44, "y": -382.33, "color": "fa1c1cff"}, {"name": "bone151", "parent": "bone150", "x": 451.11, "y": 967.54, "color": "fa1c1cff"}, {"name": "bone152", "parent": "bone151", "length": 402.44, "rotation": 149.8, "x": -14.25, "y": 11.4, "color": "fa1c1cff"}, {"name": "bone153", "parent": "bone152", "length": 353.08, "rotation": 77.49, "x": 402.44, "color": "fa1c1cff"}, {"name": "bone154", "parent": "bone153", "length": 242.79, "rotation": 33.25, "x": 353.08, "color": "fa1c1cff"}, {"name": "bone159", "parent": "bone151", "length": 359.78, "rotation": -176.82, "x": -470.36, "y": -781.17, "color": "fa1c1cff"}, {"name": "bone161", "parent": "bone159", "length": 206.79, "rotation": -18.37, "x": 626.57, "y": -29.1, "color": "fa1c1cff"}, {"name": "bone162", "parent": "bone161", "length": 219.08, "rotation": -39.88, "x": 206.79, "color": "fa1c1cff"}, {"name": "bone201", "parent": "bone152", "length": 437.79, "rotation": 166.3, "x": 318.46, "y": -102.43, "color": "fa1c1cff"}, {"name": "bone164", "parent": "bone201", "length": 293.65, "rotation": -82.25, "x": 622.29, "y": -260.68, "color": "fa1c1cff"}, {"name": "bone166", "parent": "bone164", "length": 90.2, "rotation": -31.69, "x": 247.37, "y": -135.92, "color": "fa1c1cff"}, {"name": "bone167", "parent": "bone166", "length": 116.07, "rotation": 46.74, "x": 90.2, "color": "fa1c1cff"}, {"name": "bone168", "parent": "bone153", "length": 292.03, "rotation": 5.84, "x": 446.88, "y": 111.71, "color": "fa1c1cff"}, {"name": "bone169", "parent": "bone168", "length": 142.75, "rotation": 20.48, "x": 292.03, "color": "fa1c1cff"}, {"name": "bone170", "parent": "bone169", "length": 121.93, "rotation": -51.28, "x": 142.75, "color": "fa1c1cff"}, {"name": "bone171", "parent": "bone170", "length": 66.74, "rotation": 62.48, "x": 122.69, "y": 1.86, "color": "fa1c1cff"}, {"name": "bone172", "parent": "bone169", "length": 95.68, "rotation": 46.73, "x": 121.73, "y": 35.8, "color": "fa1c1cff"}, {"name": "bone173", "parent": "bone172", "length": 76.13, "rotation": -67.82, "x": 95.68, "color": "fa1c1cff"}, {"name": "bone174", "parent": "bone154", "length": 77.36, "rotation": -145.07, "x": -37.29, "y": -77.34, "color": "fa1c1cff"}, {"name": "bone176", "parent": "bone174", "length": 52.09, "rotation": -8.76, "x": 142.85, "y": -1.72, "color": "fa1c1cff"}, {"name": "bone178", "parent": "bone154", "length": 79.63, "rotation": 160.76, "x": -57.8, "y": 146.85, "color": "fa1c1cff"}, {"name": "bone180", "parent": "bone178", "length": 58.18, "rotation": -2.26, "x": 145.86, "y": 0.24, "color": "fa1c1cff"}, {"name": "bone182", "parent": "bone154", "length": 57.44, "rotation": 121.57, "x": 65.5, "y": 185.94, "color": "fa1c1cff"}, {"name": "bone184", "parent": "bone182", "length": 58.84, "rotation": 25.18, "x": 114.98, "y": 17.91, "color": "fa1c1cff"}, {"name": "bone186", "parent": "bone154", "length": 63.19, "rotation": 99.46, "x": 171.86, "y": 195.24, "color": "fa1c1cff"}, {"name": "bone188", "parent": "bone186", "length": 43.53, "rotation": -6.58, "x": 113.08, "y": -4.99, "color": "fa1c1cff"}, {"name": "bone190", "parent": "bone154", "length": 55.9, "rotation": -103.29, "x": 106.51, "y": -110.69, "color": "fa1c1cff"}, {"name": "bone192", "parent": "bone190", "length": 42.59, "rotation": -28.59, "x": 105.57, "y": -15.24, "color": "fa1c1cff"}, {"name": "bone194", "parent": "bone154", "length": 51.55, "rotation": -80.54, "x": 235.83, "y": -67.22, "color": "fa1c1cff"}, {"name": "bone196", "parent": "bone194", "length": 44.93, "rotation": 38.99, "x": 98.12, "y": 11.64, "color": "fa1c1cff"}, {"name": "bone197", "parent": "bone154", "length": 183.36, "rotation": -3.67, "x": 261.64, "y": 94.41, "color": "fa1c1cff"}, {"name": "bone198", "parent": "bone197", "length": 51.62, "rotation": 15.34, "x": 193.02, "y": 2.25, "color": "fa1c1cff"}, {"name": "bone200", "parent": "bone198", "length": 38.98, "rotation": -16.95, "x": 92.95, "y": -9.53, "color": "fa1c1cff"}, {"name": "bone202", "parent": "bone154", "length": 47.85, "rotation": -55.39, "x": 316.94, "y": 81.9, "color": "fa1c1cff"}, {"name": "bone204", "parent": "bone202", "length": 47.32, "rotation": -57.94, "x": 95.29, "y": -15.43, "color": "fa1c1cff"}, {"name": "bone206", "parent": "bone204", "length": 30.88, "rotation": 56.43, "x": 78.38, "y": 10.55, "color": "fa1c1cff"}, {"name": "bone207", "parent": "bone206", "length": 36.41, "rotation": 37.32, "x": 30.88, "color": "fa1c1cff"}, {"name": "bone208", "parent": "bone154", "length": 48.46, "rotation": 178.42, "x": -10.69, "y": 24.53, "color": "fa1c1cff"}, {"name": "bone210", "parent": "bone208", "length": 31.13, "rotation": -15.52, "x": 83.47, "y": -3.8, "color": "fa1c1cff"}, {"name": "bone212", "parent": "bone154", "length": 38.91, "rotation": -143.97, "x": -16.99, "y": -57.67, "color": "fa1c1cff"}, {"name": "bone214", "parent": "bone212", "length": 32.08, "rotation": 22.83, "x": 73.15, "y": 8.82, "color": "fa1c1cff"}, {"name": "bone216", "parent": "root", "length": 299.9, "rotation": 90, "x": -419.37, "y": 564.07}, {"name": "bone217", "parent": "root", "length": 328.08, "rotation": 88.91, "x": -0.75, "y": 510.96}, {"name": "bone218", "parent": "root", "length": 499.1, "rotation": -43.54, "x": -1068.25, "y": 754.29}, {"name": "bone219", "parent": "root", "length": 400.07, "rotation": 88.21, "x": -250.67, "y": 485.97}, {"name": "bone220", "parent": "root", "length": 491.54, "rotation": 88.52, "x": 208.75, "y": 435.72}, {"name": "bone221", "parent": "root", "length": 521.25, "rotation": 88.78, "x": -85.65, "y": 540.88}, {"name": "bone222", "parent": "root", "length": 499.1, "rotation": -43.54, "x": -617.18, "y": 726.46}], "slots": [{"name": "root", "bone": "root", "attachment": "root"}, {"name": "beijing-tiankong", "bone": "root", "attachment": "beijing-tiankong"}, {"name": "beijing-bei2", "bone": "bone221", "attachment": "beijing-bei"}, {"name": "beijing-bei", "bone": "root"}, {"name": "beijing-zhongjian2", "bone": "bone217", "attachment": "beijing-zhongjian"}, {"name": "beijing-qian2", "bone": "bone216", "attachment": "beijing-qian1"}, {"name": "beijing-qian3", "bone": "bone219", "attachment": "beijing-qian"}, {"name": "texiaoixiantiaoguang2", "bone": "bone218", "color": "ffffff00", "attachment": "texiaoixiantiaoguang", "blend": "additive"}, {"name": "texiaoixiantiaoguang3", "bone": "bone222", "color": "ffffff00", "attachment": "texiaoixiantiaoguang", "blend": "additive"}, {"name": "texiao-guang2", "bone": "bone220", "attachment": "texiao-guang", "blend": "additive"}, {"name": "hooddoupeng-youbei2", "bone": "bone44", "attachment": "hooddoupeng-youbei"}, {"name": "hooddoupeng-bei2", "bone": "bone92", "attachment": "hooddoupeng-bei"}, {"name": "hooddoupeng-qian2", "bone": "bone87", "attachment": "hooddoupeng-qian"}, {"name": "hoodlanzi-di2", "bone": "bone31", "attachment": "<PERSON><PERSON><PERSON>-<PERSON>"}, {"name": "hoodmianbao2", "bone": "bone126", "attachment": "hoodmianbao"}, {"name": "bone32", "bone": "bone31", "attachment": "hoodlanzibu"}, {"name": "hoodbi-youbei2", "bone": "bone17", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "bone31", "bone": "bone31", "attachment": "Layer 3"}, {"name": "hoodshenti2", "bone": "bone2", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "bone33", "bone": "bone31", "attachment": "<PERSON>hua"}, {"name": "hoodbi-zuo2", "bone": "bone15", "attachment": "<PERSON><PERSON>-zuo"}, {"name": "hoodsh<PERSON>zhang-zuo3", "bone": "bone146", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>-zuo"}, {"name": "hoodshouzhang-zuo2", "bone": "bone16"}, {"name": "head hood back2", "bone": "bone3", "attachment": "head hood back"}, {"name": "bone3", "bone": "bone3", "attachment": "<PERSON><PERSON><PERSON>-qian"}, {"name": "hoodtoufa-youbei2", "bone": "bone83", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "hoodmaozi-bei2", "bone": "bone109", "attachment": "<PERSON><PERSON><PERSON><PERSON>-bei"}, {"name": "hoodmaozi-you2", "bone": "bone121", "attachment": "<PERSON><PERSON><PERSON><PERSON>-you"}, {"name": "bone8", "bone": "bone4", "attachment": "hoodlian"}, {"name": "bone9", "bone": "bone4", "color": "ffffffa4", "attachment": "hoodlian-ying", "blend": "multiply"}, {"name": "hoodtoufa-zuobei2", "bone": "bone79", "attachment": "<PERSON><PERSON>ufa<PERSON><PERSON><PERSON><PERSON>"}, {"name": "fabei2", "bone": "bone73", "attachment": "fabei"}, {"name": "bone50", "bone": "bone50", "attachment": "sedai-bei"}, {"name": "bone7", "bone": "bone4", "attachment": "fabian"}, {"name": "bone5", "bone": "bone4", "attachment": "hoodyanying"}, {"name": "liuhai2", "bone": "bone4", "attachment": "liu<PERSON>"}, {"name": "sedai-qian2", "bone": "bone50", "attachment": "sedai-qian"}, {"name": "hoodtoufa-shang2", "bone": "bone57", "attachment": "hood<PERSON>ufa-shang"}, {"name": "hoodtoufa-qian2", "bone": "bone131", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ian"}, {"name": "hoodmaozi-zuoqian2", "bone": "bone107", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "bone4", "bone": "bone4", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "bone6", "bone": "bone4", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "hoodzui2", "bone": "bone106", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "hoodbi-youqian2", "bone": "bone18", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "hoodtuzier-you2", "bone": "bone26", "attachment": "hoodtuzier-you"}, {"name": "hoodtuzitou2", "bone": "bone20", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "hoodtuzier-zuo2", "bone": "bone21", "attachment": "<PERSON><PERSON><PERSON>-zuo"}, {"name": "langweiba2", "bone": "bone159", "attachment": "langweiba"}, {"name": "langshou-bei2", "bone": "bone168", "attachment": "lang<PERSON><PERSON>-bei"}, {"name": "langshouzhi-qian2", "bone": "bone169", "attachment": "lang<PERSON><PERSON><PERSON>-qian"}, {"name": "langshenti2", "bone": "bone151", "attachment": "lang<PERSON><PERSON>"}, {"name": "langshou-you2", "bone": "bone201", "attachment": "langshou-you"}, {"name": "langshou-qian2", "bone": "bone164", "attachment": "lang<PERSON><PERSON>-qian"}, {"name": "langshizhi2", "bone": "bone166", "attachment": "langshizhi"}, {"name": "bone154", "bone": "bone154", "attachment": "langer<PERSON>o"}, {"name": "langxiaer2", "bone": "bone197", "attachment": "lang<PERSON><PERSON>"}, {"name": "langshetou2", "bone": "bone202", "attachment": "la<PERSON><PERSON><PERSON><PERSON>"}, {"name": "bone159", "bone": "bone154", "attachment": "langyan<PERSON><PERSON>"}, {"name": "bone158", "bone": "bone154", "attachment": "lang<PERSON><PERSON><PERSON>"}, {"name": "bone160", "bone": "bone154", "attachment": "lang<PERSON>u"}, {"name": "bone157", "bone": "bone154", "attachment": "langlian1"}, {"name": "bone155", "bone": "bone154", "attachment": "langlian"}, {"name": "bone161", "bone": "bone154", "attachment": "lang<PERSON><PERSON>"}, {"name": "langertou2", "bone": "bone154", "attachment": "la<PERSON><PERSON><PERSON>"}, {"name": "bone156", "bone": "bone154", "color": "ffffff3a", "attachment": "lang<PERSON>uang", "blend": "additive"}, {"name": "bg", "bone": "root", "attachment": "bg"}], "skins": [{"name": "default", "attachments": {"beijing-bei2": {"beijing-bei": {"x": -225.34, "y": -74.96, "scaleX": 5, "scaleY": 5, "rotation": -88.78, "width": 469, "height": 198}}, "beijing-qian2": {"beijing-qian1": {"x": -184.57, "y": -396.87, "scaleX": 2.5, "scaleY": 2.5, "rotation": -90, "width": 944, "height": 339}}, "beijing-qian3": {"beijing-qian": {"x": -164.04, "y": -240.42, "scaleX": 2.5, "scaleY": 2.5, "rotation": -88.21, "width": 937, "height": 397}}, "beijing-tiankong": {"beijing-tiankong": {"x": -17.5, "y": 335, "scaleX": 10, "scaleY": 10, "width": 235, "height": 95}}, "beijing-zhongjian2": {"beijing-zhongjian": {"x": -49.25, "y": 14.81, "scaleX": 2.5, "scaleY": 2.5, "rotation": -88.91, "width": 938, "height": 265}}, "bg": {"bg": {"x": 270, "y": 310, "scaleX": 2, "scaleY": 1.8, "width": 760, "height": 540}}, "bone3": {"hoodyifu-qian": {"type": "mesh", "uvs": [0.9617, 0.25017, 1, 0.42393, 1, 0.76343, 0.86179, 0.99999, 0.69537, 0.99999, 0.32991, 0.93413, 0.13985, 0.66044, 0.10685, 0.37548, 0.0001, 0, 0.14081, 0], "triangles": [1, 6, 7, 7, 9, 0, 7, 8, 9, 4, 5, 6, 3, 4, 2, 6, 2, 4, 2, 6, 1, 1, 7, 0], "vertices": [1, 68, -0.99, 40.2, 1, 2, 68, 41.91, 45.99, 0.92459, 69, -52.93, 42.06, 0.07541, 2, 69, 29.16, 53.15, 0.8445, 70, -36.66, 50.77, 0.1555, 1, 70, 26.84, 33.68, 1, 1, 70, 35.76, -3.2, 1, 1, 73, 38.78, -7.22, 1, 3, 73, -11.67, -68.8, 0.2401, 71, 115.8, -64.07, 0.08011, 72, 37.6, -66.32, 0.67978, 2, 71, 47.3, -78.14, 0.68598, 72, -31.1, -79.42, 0.31402, 1, 71, -41.61, -111.07, 1, 1, 71, -44.66, -79.11, 1], "hull": 10, "edges": [0, 2, 6, 8, 16, 18, 18, 0, 14, 16, 2, 4, 12, 14, 10, 12, 8, 10, 4, 6, 4, 12], "width": 103, "height": 110}}, "bone4": {"hoodmeimao": {"x": 163.62, "y": -23.93, "scaleX": 2.5, "scaleY": 2.5, "rotation": 273.28, "width": 114, "height": 70}}, "bone5": {"hoodyanying": {"x": 134.14, "y": -25.12, "scaleX": 2.5, "scaleY": 2.5, "rotation": 273.28, "width": 107, "height": 46}}, "bone6": {"hoodyanzhu": {"x": 131.67, "y": -25.77, "scaleX": 2.5, "scaleY": 2.5, "rotation": 273.28, "width": 84, "height": 35}}, "bone7": {"fabian": {"type": "mesh", "uvs": [0.62108, 0.06297, 0.75457, 0.22848, 0.89687, 0.38479, 1, 0.35721, 1, 0.41054, 0.92373, 0.46939, 0.78245, 0.39031, 0.90273, 0.6349, 0.88807, 0.91627, 0.81765, 1, 0.52713, 1, 0.64021, 0.71686, 0.58406, 0.42792, 0.46318, 0.15965, 0.18035, 0.33328, 0.20797, 0.68535, 0.34846, 1, 0.1204, 0.99472, 0.00526, 0.7492, 0, 0.46824, 0.04447, 0.21026, 0.24255, 0, 0.50813, 0], "triangles": [17, 15, 16, 17, 18, 15, 18, 19, 15, 19, 14, 15, 19, 20, 14, 14, 21, 13, 14, 20, 21, 8, 9, 11, 9, 10, 11, 8, 11, 7, 11, 6, 7, 6, 1, 2, 6, 2, 5, 5, 2, 4, 2, 3, 4, 1, 12, 13, 11, 12, 6, 12, 1, 6, 1, 13, 0, 13, 22, 0, 13, 21, 22], "vertices": [1, 37, 89.4, 48.61, 1, 3, 37, 182.38, 22.74, 0.05428, 38, -10.42, 52.98, 0.29407, 66, -26.19, 27.77, 0.65164, 1, 66, 62.16, 12.61, 1, 1, 66, 93.93, 49.47, 1, 1, 66, 106.59, 31.64, 1, 1, 66, 83.79, -6.55, 1, 2, 38, 56.47, 41.43, 0.29449, 66, 23.92, -18.03, 0.70551, 2, 38, 172.6, 61.24, 0.05332, 39, -13.91, 75.8, 0.94668, 1, 39, 101.69, 74.39, 1, 1, 39, 137.89, 40.07, 1, 1, 39, 144.61, -86.49, 1, 2, 38, 153.27, -76.46, 0.0835, 39, 26.8, -57.17, 0.9165, 1, 38, 32.61, -58.9, 1, 1, 37, 37.81, -25.53, 1, 2, 40, 78.28, 41.96, 0.88354, 41, -64.74, 63.12, 0.11646, 2, 41, 79.76, 50.62, 0.68141, 42, -27.08, 57.87, 0.31859, 1, 42, 113.54, 12.21, 1, 1, 42, 26.01, -65.81, 1, 2, 41, 86.4, -56.61, 0.86505, 42, -85.78, -32.09, 0.13495, 1, 41, 2.28, -43.68, 1, 1, 40, 67.64, -43.53, 1, 2, 37, -93.11, -23.57, 0.2889, 40, -56.91, 4.29, 0.7111, 1, 37, 26.12, 42.89, 1], "hull": 23, "edges": [32, 34, 36, 38, 28, 40, 42, 44, 44, 0, 6, 8, 18, 20, 16, 18, 4, 10, 30, 36, 0, 26, 22, 14, 30, 32, 28, 30, 40, 42, 38, 40, 34, 36, 20, 22, 22, 24, 24, 26, 0, 2, 2, 4, 4, 6, 10, 12, 8, 10, 12, 14, 14, 16, 26, 28], "width": 231, "height": 185}}, "bone8": {"hoodlian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.60524, 0.62302], "triangles": [4, 2, 3, 1, 2, 4, 4, 3, 0, 1, 4, 0], "vertices": [1.71, -187.47, -19.86, 188.91, 382.48, 211.98, 404.05, -164.41, 144.87, -30.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 170, "height": 181}}, "bone9": {"hoodlian-ying": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.59412, 0.73358], "triangles": [4, 2, 3, 1, 2, 4, 4, 3, 0, 1, 4, 0], "vertices": [57.67, -190.83, 39.42, 183.72, 378.01, 200.23, 396.27, -174.33, 140.47, -34.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 169, "height": 153}}, "bone31": {"Layer 3": {"x": 60.05, "y": 13.93, "scaleX": 2.5, "scaleY": 2.5, "rotation": 71.2, "width": 72, "height": 98}}, "bone32": {"hoodlanzibu": {"type": "mesh", "uvs": [1, 0.0767, 0.9824, 0.17151, 0.87729, 0.17304, 0.8245, 0.26938, 0.82036, 0.74654, 0.62253, 0.95447, 0.56398, 0.99999, 0.46542, 0.89419, 0.26622, 0.72463, 1e-05, 0.7253, 0.01608, 0.60695, 0.15804, 0.43597, 0.28289, 0.0586, 0.66614, 0.09008, 0.84975, 0, 0.95074, 0], "triangles": [9, 10, 8, 10, 11, 8, 11, 3, 8, 7, 8, 4, 6, 7, 5, 5, 7, 4, 13, 11, 12, 4, 8, 3, 3, 13, 2, 13, 14, 2, 2, 14, 15, 3, 11, 13, 1, 2, 0, 0, 2, 15], "vertices": [1, 21, 86.3, -12.22, 1, 1, 21, 72.57, -31.85, 1, 1, 21, 44.51, -20.49, 1, 3, 21, 21.28, -36.56, 0.818, 22, 20.76, 103.91, 0.11381, 23, -85.82, 84.05, 0.0682, 2, 22, 126.19, 51.22, 0.11229, 23, 30.98, 68.24, 0.88771, 2, 23, 74.87, 5.34, 0.066, 19, 226.7, 10.86, 0.934, 1, 19, 231.92, -8.72, 1, 1, 19, 198.03, -27.17, 1, 2, 19, 139.9, -67.99, 0.19301, 24, 17.63, 40.82, 0.80699, 1, 25, 39.27, 0.33, 1, 1, 25, 15.78, -17.68, 1, 2, 19, 62.36, -74.51, 0.15167, 24, 33.76, -35.3, 0.84833, 1, 19, -14.29, -10.44, 1, 2, 21, -3.65, 21.92, 0.76738, 20, 75.66, 27.62, 0.23262, 1, 21, 53.72, 21.99, 1, 1, 21, 80.54, 10.74, 1], "hull": 16, "edges": [2, 4, 4, 6, 10, 12, 14, 12, 18, 20, 28, 30, 2, 0, 30, 0, 20, 22, 26, 28, 24, 26, 6, 8, 14, 16, 16, 18, 22, 24, 8, 10], "width": 130, "height": 111}}, "bone33": {"hoodhua": {"type": "mesh", "uvs": [1, 0.31683, 0.90477, 0.61655, 0.67098, 0.74058, 0.51511, 1, 0, 1, 0, 0.49253, 0, 0, 0.43231, 0, 0.98052, 0.0689, 0.51024, 0.4443], "triangles": [5, 7, 9, 5, 6, 7, 2, 3, 9, 9, 3, 5, 3, 4, 5, 2, 9, 1, 1, 9, 0, 9, 8, 0, 9, 7, 8], "vertices": [1, 75, 44.79, -30.23, 1, 2, 75, -2.64, -47.21, 0.85047, 77, -46.78, 43.85, 0.14953, 2, 75, -34.34, -35.2, 0.37616, 77, -15.2, 31.52, 0.62384, 1, 77, 30.48, 38.58, 1, 1, 77, 61.97, -12.21, 1, 2, 77, -8.76, -56.06, 0.35353, 76, -4.53, 49.88, 0.64647, 1, 76, 73.69, 29.73, 1, 2, 75, 53.42, 53.21, 0.1108, 76, 61.18, -18.83, 0.8892, 1, 75, 77.92, -6.55, 1, 2, 75, -3.28, 6.56, 0.70573, 76, -11.63, -9.41, 0.29427], "hull": 9, "edges": [12, 14, 0, 16, 0, 2, 2, 4, 6, 8, 4, 6, 8, 10, 10, 12, 14, 16], "width": 52, "height": 74}}, "bone50": {"sedai-bei": {"type": "mesh", "uvs": [1, 1, 0.36631, 0.82821, 1e-05, 0.71877, 0.01366, 0.30116, 0.56975, 0, 0.99999, 0.00439], "triangles": [1, 3, 4, 2, 3, 1, 1, 4, 5, 0, 1, 5], "vertices": [1, 33, 61.72, -39.92, 1, 2, 33, -22.16, -64.6, 0.08566, 32, 20.59, -44.54, 0.91434, 1, 32, -25.53, -23.32, 1, 1, 32, -14.88, 19.26, 1, 1, 32, 70.6, 35.63, 1, 1, 33, 12.16, 52.12, 1], "hull": 6, "edges": [4, 2, 4, 6, 6, 8, 8, 10, 10, 0, 0, 2], "width": 61, "height": 47}}, "bone154": {"langerduo": {"type": "mesh", "uvs": [0.896, 0.54321, 0.79103, 1, 0.04106, 0.88845, 0.01038, 0.49031, 0, 0, 0.08417, 0.02367, 0.21662, 0.39808, 0.39514, 0.73064, 0.48582, 0.76356, 0.69091, 0.3951, 0.85271, 0.12372, 0.99531, 0.07101], "triangles": [3, 4, 5, 7, 3, 6, 3, 5, 6, 2, 7, 8, 7, 2, 3, 0, 10, 11, 8, 9, 0, 9, 10, 0, 1, 2, 8, 1, 8, 0], "vertices": [2, 98, 148.78, -58.62, 0.16635, 99, 5.23, -58.7, 0.83365, 1, 98, -12.46, -85.64, 1, 1, 96, 10.87, 94.9, 1, 2, 96, 139.12, 51.22, 0.24222, 97, -11.76, 51.75, 0.75778, 1, 97, 148.51, 9.22, 1, 1, 97, 128.46, -29.76, 1, 2, 96, 121.87, -57.51, 0.38825, 97, -12.24, -58.34, 0.61175, 2, 98, -29.97, 135.88, 0.27387, 96, -18.92, -91.71, 0.72613, 2, 98, -17.42, 89.81, 0.69331, 96, -48.92, -128.85, 0.30669, 2, 98, 142.25, 57.53, 0.22201, 99, -5.87, 57.1, 0.77799, 1, 99, 115.41, 33.25, 1, 1, 99, 168.25, -20.19, 1], "hull": 12, "edges": [8, 10, 14, 16, 20, 22, 12, 6, 16, 2, 18, 0, 4, 6, 6, 8, 10, 12, 12, 14, 16, 18, 18, 20, 22, 0, 0, 2, 2, 4], "width": 230, "height": 152}}, "bone155": {"langlian": {"x": 141.08, "y": -84.99, "scaleX": 2.5, "scaleY": 2.5, "rotation": 99.46, "width": 59, "height": 47}}, "bone156": {"langyanguang": {"x": 90.12, "y": 13.98, "scaleX": 2.5, "scaleY": 2.5, "rotation": 99.46, "width": 147, "height": 69}}, "bone157": {"langlian1": {"x": 111.16, "y": 140.15, "scaleX": 2.5, "scaleY": 2.5, "rotation": 99.46, "width": 75, "height": 54}}, "bone158": {"langyanzhu": {"x": 88.72, "y": 16.28, "scaleX": 2.5, "scaleY": 2.5, "rotation": 99.46, "width": 92, "height": 24}}, "bone159": {"langyanbai": {"x": 94.89, "y": 18.83, "scaleX": 2.5, "scaleY": 2.5, "rotation": 99.46, "width": 112, "height": 44}}, "bone160": {"langtou": {"type": "mesh", "uvs": [0.10329, 0.54364, 0.08672, 0.43119, 0.09299, 0.26042, 0.18983, 0.34948, 0.30951, 0.31993, 0.60411, 0.34175, 0.75693, 0.35019, 0.87661, 0.27421, 0.83427, 0.45149, 0.81033, 0.50003, 0.92449, 0.4937, 1, 0.43387, 1, 0.5342, 0.90896, 0.67221, 0.9622, 0.73177, 0.99079, 0.80744, 0.85636, 0.76734, 0.61147, 0.83699, 0.5949, 1, 0.2211, 1, 0.20272, 0.82644, 0.14564, 0.83066, 0.08119, 0.85387, 0.09592, 0.70825, 0, 0.53942, 0.84648, 0.67579, 0.4281, 0.5935], "triangles": [17, 19, 20, 20, 26, 17, 17, 25, 16, 17, 9, 25, 16, 14, 15, 16, 13, 14, 16, 25, 13, 13, 25, 10, 13, 10, 12, 10, 25, 9, 10, 11, 12, 9, 5, 6, 9, 6, 8, 8, 6, 7, 9, 17, 5, 18, 19, 17, 26, 5, 17, 20, 4, 26, 26, 4, 5, 4, 0, 3, 0, 1, 3, 1, 2, 3, 4, 20, 0, 22, 23, 21, 21, 23, 20, 0, 20, 23, 23, 24, 0], "vertices": [3, 107, -53.24, -137.62, 0.24989, 104, 109.96, 52.5, 0.46239, 105, -28.56, 61.58, 0.28772, 1, 105, 35.54, 25.94, 1, 1, 105, 118.39, -46.26, 1, 3, 104, 99.43, -87.3, 0.20936, 105, 29.09, -66.22, 0.73175, 82, -1.3, -187.39, 0.05888, 3, 104, 25.43, -138.93, 0.38182, 105, -11.18, -146.96, 0.13072, 82, -34.54, -103.51, 0.48746, 3, 82, -56.4, 112.96, 0.40972, 100, 1.65, 142.07, 0.50076, 101, -49.73, 160.58, 0.08952, 2, 100, 103.95, 94.65, 0.25312, 101, 22.68, 74.14, 0.74688, 1, 101, 118.4, 42.46, 1, 1, 101, 13.47, -11.92, 1, 3, 100, 104.15, -9.42, 0.40447, 101, -21.43, -20.13, 0.43809, 103, -20.96, 140.41, 0.15744, 3, 100, 183.63, -37.32, 0.23765, 101, 38.63, -79.19, 0.11476, 103, 62.15, 154.1, 0.64759, 3, 100, 245.09, -24.1, 0.21235, 101, 99.88, -93.38, 0.10812, 103, 109.72, 195.21, 0.67953, 3, 100, 219.73, -84.26, 0.21319, 101, 51.33, -137.03, 0.10378, 103, 116.35, 130.26, 0.68303, 2, 100, 129.83, -139.35, 0.08007, 103, 63.94, 38.76, 0.91993, 1, 103, 107.31, 5.22, 1, 1, 103, 133.82, -40.7, 1, 1, 103, 32.44, -26.45, 1, 2, 82, 256.81, 170.67, 0.39499, 102, -38.2, -79.76, 0.60501, 2, 82, 362.21, 175.85, 0.67663, 102, -50.42, -184.58, 0.32337, 2, 82, 402.71, -102.17, 0.29184, 106, 61.91, 158.87, 0.70816, 2, 107, 4.22, 49.82, 0.39183, 106, 70.05, 53.02, 0.60817, 1, 107, 38.62, 25.46, 1, 1, 107, 84.93, 7.18, 1, 2, 107, 17.58, -58.76, 0.80426, 104, 74.04, 152.21, 0.19574, 3, 107, 4.22, -187.62, 0.41115, 104, 181.21, 79.43, 0.44233, 105, 21.12, 119.32, 0.14652, 2, 100, 86.3, -124.15, 0.08736, 103, 18.46, 31.2, 0.91264, 5, 104, -123.2, -10.51, 0.18609, 105, -203.13, -105.32, 0.06371, 82, 124.6, 11.62, 0.41897, 102, -173.35, 76.8, 0.26977, 106, -96.06, -96.76, 0.06146], "hull": 25, "edges": [44, 42, 42, 40, 40, 38, 36, 34, 16, 18, 20, 22, 22, 24, 28, 30, 26, 28, 0, 46, 12, 18, 18, 50, 50, 32, 4, 6, 6, 8, 2, 4, 0, 2, 48, 0, 46, 48, 44, 46, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 24, 26, 30, 32, 32, 34, 36, 38, 40, 8, 10, 34], "width": 332, "height": 289}}, "bone161": {"langbizi": {"type": "mesh", "uvs": [0.7403, 0, 1, 0.24828, 1, 0.87355, 0.71669, 1, 0.40469, 0.90589, 0.22033, 1, 0, 0.87894, 0, 0.35609, 0.25814, 0.00539, 0.41414, 0.41538], "triangles": [9, 8, 0, 9, 0, 1, 7, 8, 9, 9, 1, 2, 6, 7, 9, 4, 6, 9, 3, 4, 9, 5, 6, 4, 2, 3, 9], "vertices": [150.63, 52.38, 182.81, 106.98, 283.96, 123.84, 313.12, 74.99, 307.49, 14.9, 328.38, -16.57, 315.57, -60.47, 230.99, -74.57, 166.33, -36.41, 227.85, 3.42], "hull": 9, "edges": [12, 10, 10, 8, 8, 6, 4, 6, 12, 14, 14, 16, 4, 2, 2, 0, 0, 16], "width": 84, "height": 74}}, "fabei2": {"fabei": {"type": "mesh", "uvs": [0.5521, 0.28535, 0.57382, 0.64041, 0.74349, 0.99999, 0.35235, 0.99999, 0.05377, 0.87444, 0, 0.70147, 0, 0.48273, 0.15671, 0.20652, 0.4991, 0, 1, 0], "triangles": [4, 5, 1, 3, 4, 1, 3, 1, 2, 6, 1, 5, 1, 6, 0, 7, 8, 0, 0, 6, 7, 0, 8, 9], "vertices": [2, 43, 97.49, 64.94, 0.23276, 44, -3.38, 44.84, 0.76724, 1, 45, 38.75, 43.89, 1, 1, 45, 166.06, 41.17, 1, 1, 45, 141.68, -36.04, 1, 1, 45, 76.32, -73.69, 1, 2, 44, 172.04, -44.55, 0.11519, 45, -9.97, -64.63, 0.88481, 2, 44, 93.83, -56.84, 0.88449, 45, -85.48, -40.79, 0.11551, 2, 43, 142.91, -7.74, 0.15753, 44, -26.1, -37.81, 0.84247, 1, 43, 35.08, -38.36, 1, 1, 43, -45.5, 15.36, 1], "hull": 10, "edges": [16, 18, 14, 12, 10, 12, 10, 2, 8, 10, 4, 6, 8, 6, 14, 0, 0, 2, 2, 4, 14, 16, 18, 0], "width": 93, "height": 187}}, "head hood back2": {"head hood back": {"x": 261.22, "y": -15.91, "scaleX": 2.5, "scaleY": 2.5, "rotation": -75.16, "width": 128, "height": 76}}, "hoodbi-youbei2": {"hoodbi-youbei": {"x": 97.45, "y": -1.78, "scaleX": 2.5, "scaleY": 2.5, "rotation": 31.64, "width": 89, "height": 64}}, "hoodbi-youqian2": {"hoodbi-youqian": {"type": "mesh", "uvs": [1, 0.09721, 0.71209, 0.22674, 0.85316, 0.34997, 0.58064, 0.49081, 0.99999, 0.99623, 0.36903, 0.99623, 0.07728, 0.45348, 0, 0.3642, 0.01282, 0.22925, 0.27926, 0.18524, 0.51652, 0, 0.99038, 1e-05], "triangles": [5, 3, 4, 5, 6, 3, 2, 3, 8, 8, 3, 6, 6, 7, 8, 2, 9, 1, 2, 8, 9, 9, 10, 1, 1, 11, 0, 1, 10, 11], "vertices": [1, 11, 150.83, -35.44, 1, 1, 11, 95.59, -19.87, 1, 1, 11, 66.3, -54.19, 1, 2, 11, 8.51, -42.07, 0.35823, 10, 140.66, -35.51, 0.64177, 1, 10, -36.73, -28.57, 1, 1, 10, -8.1, 48.97, 1, 2, 11, -8.45, 22.86, 0.67042, 10, 175.19, 22.03, 0.32958, 1, 11, 14.16, 44.78, 1, 1, 11, 55.62, 62.55, 1, 1, 11, 83.84, 37.3, 1, 1, 11, 153.06, 35.7, 1, 1, 11, 179.63, -20.4, 1], "hull": 12, "edges": [20, 22, 20, 18, 18, 16, 0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 14, 12, 8, 10, 12, 10, 16, 4], "width": 59, "height": 150}}, "hoodbi-zuo2": {"hoodbi-zuo": {"type": "mesh", "uvs": [0.72806, 0.60758, 0.47094, 0.708, 0.03755, 0.99999, 0, 0.81087, 0.2807, 0.43017, 0.43047, 0.08154, 0.70878, 1e-05, 0.93956, 0.06021], "triangles": [3, 4, 1, 2, 3, 1, 0, 6, 7, 5, 6, 0, 4, 5, 0, 1, 4, 0], "vertices": [1, 7, 84.76, 100.23, 1, 2, 7, 185.9, 54.97, 0.61777, 8, -4.65, 54.8, 0.38223, 1, 8, 192.53, 28.4, 1, 1, 8, 176.31, -25.89, 1, 2, 7, 196.59, -57.06, 0.35089, 8, 18.85, -55.26, 0.64911, 1, 7, 84.82, -95.42, 1, 1, 7, -19.71, -40.37, 1, 1, 7, -83.1, 33.78, 1], "hull": 8, "edges": [14, 0, 0, 2, 12, 14, 12, 10, 10, 8, 4, 6, 8, 2, 6, 8, 2, 4], "width": 187, "height": 130}}, "hooddoupeng-bei2": {"hooddoupeng-bei": {"type": "mesh", "uvs": [0.26113, 0.21301, 0.54731, 0.05465, 0.78621, 0.07815, 0.85618, 0.2875, 0.80957, 0.7452, 0.7049, 0.98956, 0, 0.74044, 1e-05, 0.18088], "triangles": [5, 6, 4, 3, 4, 1, 1, 4, 0, 3, 1, 2, 4, 6, 0, 6, 7, 0], "vertices": [1, 49, 36.31, -1.93, 1, 2, 49, -89.85, 2.23, 0.37095, 50, 33.04, -96.38, 0.62905, 1, 50, -28.48, -17.2, 1, 1, 50, -8.55, 39.19, 1, 1, 50, 90.08, 99.54, 1, 2, 49, -62.74, 234.37, 0.05641, 50, 168.35, 94.17, 0.94359, 1, 49, 185.73, 86.82, 1, 1, 49, 136.31, -47.04, 1], "hull": 8, "edges": [4, 6, 4, 2, 14, 0, 0, 2, 6, 8, 8, 10, 12, 14, 10, 12], "width": 189, "height": 112}}, "hooddoupeng-qian2": {"hooddoupeng-qian": {"type": "mesh", "uvs": [0.99694, 0.2647, 0.64794, 0.64952, 0.34347, 0.96851, 0.24372, 1, 0, 1, 1e-05, 0.39273, 0.29899, 0.42496, 0.53814, 0.31356, 0.75889, 0, 1, 0], "triangles": [3, 4, 6, 4, 5, 6, 2, 6, 1, 2, 3, 6, 6, 7, 1, 0, 8, 9, 7, 8, 0, 1, 7, 0], "vertices": [1, 48, -13.75, 32.54, 1, 2, 48, 93.31, 37.69, 0.71398, 49, -59.31, 16.83, 0.28602, 1, 49, 34.16, 55.62, 1, 1, 49, 83.06, 48, 1, 1, 49, 160.69, 19.6, 1, 1, 49, 121.46, -87.61, 1, 2, 48, 157.17, -31.22, 0.05182, 49, 14.04, -41.86, 0.94818, 2, 48, 102.08, -33.11, 0.73999, 49, -40.16, -51.89, 0.26001, 1, 48, 26.34, -50.86, 1, 1, 48, -41.94, -13.62, 1], "hull": 10, "edges": [16, 18, 18, 0, 4, 6, 0, 2, 14, 16, 8, 10, 6, 8, 10, 12, 12, 14, 2, 4], "width": 136, "height": 85}}, "hooddoupeng-youbei2": {"hooddoupeng-youbei": {"type": "mesh", "uvs": [0.91336, 0.37504, 0.40204, 1, 0.24759, 0.99999, 0, 0.86934, 0, 0.40693, 0.31524, 0.25014, 0.63679, 0.23188, 0.8667, 0, 1, 0], "triangles": [2, 3, 4, 5, 2, 4, 1, 2, 5, 5, 6, 1, 0, 1, 6, 6, 7, 0, 0, 7, 8], "vertices": [2, 27, 116.27, -11.28, 0.15359, 28, -9.56, -42.22, 0.84641, 1, 26, 44.3, -170.01, 1, 1, 26, 0.62, -184.33, 1, 1, 26, -113, -186.68, 1, 1, 26, -149.62, -75.09, 1, 1, 26, -30.54, 5.9, 1, 2, 27, 3.5, 46.58, 0.81152, 26, 102.14, 54.32, 0.18848, 1, 28, 56.88, 29.05, 1, 1, 28, 90.34, -18.95, 1], "hull": 9, "edges": [14, 16, 6, 8, 2, 4, 8, 10, 16, 0, 0, 2, 12, 14, 10, 12, 4, 6], "width": 198, "height": 114}}, "hoodlanzi-di2": {"hoodlanzi-di": {"x": 146.71, "y": 15.6, "scaleX": 2.5, "scaleY": 2.5, "rotation": 71.2, "width": 165, "height": 134}}, "hoodmaozi-bei2": {"hoodmaozi-bei": {"type": "mesh", "uvs": [0.92346, 0.09913, 0.99999, 0.97516, 0.04044, 0.99986, 0, 0.88953, 0.04382, 0.35231, 0.18978, 0.06645, 0.36972, 0, 0.65317, 0, 0.73299, 0.3235, 0.7137, 0.83852, 0.67939, 0.30703, 0.65308, 0.81024], "triangles": [10, 7, 0, 8, 10, 0, 6, 10, 5, 10, 6, 7, 11, 10, 8, 10, 4, 5, 11, 4, 10, 9, 11, 8, 3, 4, 11, 8, 0, 1, 9, 8, 1, 2, 3, 11, 2, 11, 9, 2, 9, 1], "vertices": [1, 61, 4.08, -292.41, 1, 1, 61, -46.21, -54.72, 1, 1, 61, 140.97, -27.47, 1, 1, 61, 152.16, -56.54, 1, 1, 61, 156.45, -204.92, 1, 1, 61, 132.62, -287.33, 1, 1, 61, 106.16, -305.87, 1, 1, 61, 66.52, -310.2, 1, 1, 61, 31.5, -225.19, 1, 1, 61, 15.21, -85.53, 1, 1, 61, 41.4, -228.84, 1, 1, 61, 27.62, -92.02, 1], "hull": 8, "edges": [12, 14, 12, 10, 6, 4, 8, 10, 6, 8, 14, 0, 0, 16, 16, 18, 2, 4, 0, 2, 0, 20, 20, 22], "width": 89, "height": 123}}, "hoodmaozi-you2": {"hoodmaozi-you": {"type": "mesh", "uvs": [0.74292, 0.63403, 1, 0.69988, 1, 1, 0.66937, 0.96521, 0.01261, 0.72022, 0, 0.56276, 0.44643, 0.08526, 0.69761, 0.00656], "triangles": [4, 5, 0, 3, 1, 2, 3, 0, 1, 3, 4, 0, 6, 7, 0, 5, 6, 0], "vertices": [2, 63, -32.79, -35.32, 0.06299, 64, 40.3, 15.99, 0.93701, 1, 64, 74.95, 24.15, 1, 1, 64, 94, -11.95, 1, 1, 64, 52.61, -28.45, 1, 1, 62, -37.46, -2.25, 1, 1, 62, -29.26, 17.61, 1, 1, 63, 29.24, 22.14, 1, 1, 63, 48.17, -7.67, 1], "hull": 8, "edges": [4, 2, 12, 14, 8, 10, 4, 6, 0, 2, 6, 8, 10, 12, 14, 0], "width": 60, "height": 61}}, "hoodmaozi-zuoqian2": {"hoodmaozi-zuoqian": {"type": "mesh", "uvs": [0.53965, 0.12607, 0.78222, 0.44039, 1, 0.80447, 1, 1, 0.68806, 1, 0.49895, 0.74963, 0.27578, 0.74635, 0.02658, 0.51289, 1e-05, 0.2822, 1e-05, 1e-05, 0.3353, 0], "triangles": [4, 5, 2, 4, 2, 3, 10, 8, 9, 8, 10, 7, 7, 10, 6, 10, 0, 6, 5, 6, 0, 1, 5, 0, 5, 1, 2], "vertices": [2, 60, 82.37, -83.41, 0.71202, 61, -74.07, -66.94, 0.28798, 2, 60, -36.03, -71.01, 0.57668, 59, 76.4, -82.12, 0.42332, 1, 59, -26.03, -24.02, 1, 1, 59, -34.84, 17.09, 1, 2, 60, -60.24, 52.83, 0.12976, 59, 88.39, 43.49, 0.87024, 1, 60, 32.61, 42.16, 1, 2, 60, 101.44, 75.8, 0.28098, 61, 4.16, 73.03, 0.71902, 1, 61, 122.94, 38.27, 1, 1, 61, 139.01, -9.87, 1, 1, 61, 145.6, -70.18, 1, 2, 60, 167.82, -67.67, 0.06344, 61, 10.95, -84.91, 0.93656], "hull": 11, "edges": [6, 4, 14, 16, 16, 18, 2, 4, 0, 2, 20, 0, 18, 20, 12, 14, 10, 12, 8, 10, 6, 8, 20, 12], "width": 182, "height": 97}}, "hoodmianbao2": {"hoodmianbao": {"x": 43.66, "y": 4.42, "scaleX": 2.5, "scaleY": 2.5, "rotation": -78.36, "width": 70, "height": 68}}, "hoodshenti2": {"hoodshenti": {"type": "mesh", "uvs": [0.92161, 0.19874, 0.89358, 0.45925, 0.72865, 0.61972, 0.78394, 0.69181, 0.63374, 0.67519, 0.15093, 0.57499, 0.40781, 0.46474, 0.50026, 0.02451, 0.48324, 0.27925, 0.65365, 0.46741, 0.45463, 0.52408, 0.63361, 0.60525], "triangles": [5, 6, 10, 10, 6, 9, 5, 10, 4, 4, 2, 3, 10, 11, 4, 4, 11, 2, 11, 9, 2, 11, 10, 9, 2, 9, 1, 0, 1, 9, 9, 6, 8, 8, 6, 7, 0, 9, 8, 8, 7, 0], "vertices": [1, 3, 233.49, -136.85, 1, 2, 3, 65.06, -140.93, 0.93894, 5, -1.09, 168.48, 0.06106, 2, 3, -41.53, -82.62, 0.20286, 5, 35.7, 52.69, 0.79714, 1, 5, 102.64, 77.38, 1, 1, 5, 83.01, 0.34, 1, 1, 6, 260.48, -79.21, 1, 1, 6, 35.9, -52.59, 1, 1, 3, 228.42, 125.43, 1, 2, 3, 67.79, 125.91, 0.66372, 6, -55.22, -114.96, 0.33628, 2, 3, 1.83, -36.61, 0.74878, 5, -27.44, 49.38, 0.25122, 2, 5, -49.88, -85.71, 0.09863, 6, 8.41, 22.36, 0.90137, 1, 5, 20.94, -5.84, 1], "hull": 8, "edges": [0, 2, 2, 4, 8, 6, 4, 6, 12, 14, 8, 10, 10, 12, 14, 0], "width": 253, "height": 221}}, "hoodshouzhang-zuo3": {"hoodshouzhang-zuo": {"x": 16.13, "y": -21.53, "scaleX": 2.5, "scaleY": 2.5, "rotation": -129.22, "width": 30, "height": 50}}, "hoodtoufa-qian2": {"hoodtoufa-qian": {"x": 84.91, "y": -5.66, "scaleX": 2.5, "scaleY": 2.5, "rotation": 54.61, "width": 45, "height": 66}}, "hoodtoufa-shang2": {"hoodtoufa-shang": {"type": "mesh", "uvs": [1, 0.42416, 0.99999, 0.75722, 0.87765, 1, 0.52377, 0.99999, 0.69545, 0.73701, 0.57689, 0.30433, 0, 0.28947, 0, 0, 0.64611, 0], "triangles": [5, 6, 7, 8, 5, 7, 5, 8, 0, 4, 5, 0, 3, 4, 2, 2, 4, 1, 1, 4, 0], "vertices": [2, 34, 74.89, 7.39, 0.09381, 35, -11.5, -13.09, 0.90619, 1, 34, 43.7, -15.82, 1, 1, 34, 11.13, -20.37, 1, 1, 34, -13.25, 14.83, 1, 2, 34, 24.74, 15.86, 0.91063, 35, -18.3, 37.31, 0.08937, 2, 35, 31.78, 16.35, 0.84766, 36, -18.09, 30.25, 0.15234, 1, 36, 51.46, 24.06, 1, 1, 36, 49.25, -10.9, 1, 2, 35, 53.27, -14.52, 0.76799, 36, -31.42, -4.93, 0.23201], "hull": 9, "edges": [6, 8, 12, 14, 0, 2, 8, 2, 4, 6, 2, 4, 10, 12, 8, 10, 16, 0, 14, 16], "width": 54, "height": 54}}, "hoodtoufa-youbei2": {"hoodtoufa-youbei": {"x": 75.69, "y": -22.99, "rotation": 91.08, "width": 110, "height": 98}}, "hoodtoufa-zuobei2": {"hoodtoufa-zuobei": {"x": 63.46, "y": 21.07, "rotation": 128.66, "width": 29, "height": 66}}, "hoodtuzier-you2": {"hoodtuzier-you": {"type": "mesh", "uvs": [0.91899, 0.47508, 0.92726, 0.77224, 0.63723, 0.9978, 0.40705, 1, 0.20048, 0.86726, 0.05312, 0.54629, 0.17383, 0, 0.48304, 0.01611], "triangles": [3, 4, 2, 2, 4, 1, 1, 4, 0, 4, 5, 0, 5, 7, 0, 5, 6, 7], "vertices": [2, 16, 68.73, 26.29, 0.16004, 17, -0.7, 28.25, 0.83996, 2, 17, 47.01, 21.54, 0.3109, 18, -2.45, 19.38, 0.6891, 1, 18, 21.45, -0.29, 1, 1, 18, 10.79, -10.52, 1, 2, 17, 49.47, -11.52, 0.21787, 18, -5.37, -13.64, 0.78213, 1, 17, 2.72, -19.33, 1, 1, 16, -26.77, -12.23, 1, 1, 16, -12.02, 7.54, 1], "hull": 8, "edges": [4, 2, 12, 14, 4, 6, 8, 6, 0, 10, 2, 8, 10, 12, 14, 0, 0, 2, 8, 10], "width": 21, "height": 74}}, "hoodtuzier-zuo2": {"hoodtuzier-zuo": {"type": "mesh", "uvs": [0.81565, 0.26528, 0.99999, 0.61328, 0.90504, 0.84755, 0.57619, 0.98877, 0.28308, 0.98989, 0.07047, 0.83061, 0.00385, 0.44735, 0.20806, 0.2348, 0.72125, 0, 1, 0], "triangles": [2, 5, 6, 3, 5, 2, 4, 5, 3, 1, 2, 6, 6, 7, 1, 7, 0, 1, 0, 8, 9, 7, 8, 0], "vertices": [2, 13, 32.2, 15.89, 0.77949, 14, -21.26, 15.94, 0.22051, 2, 14, 35.47, 21.85, 0.92002, 15, -29.45, 18.86, 0.07998, 1, 15, 8.76, 19.02, 1, 1, 15, 33.34, 6.45, 1, 1, 15, 35.19, -7.21, 1, 1, 15, 10.78, -20.25, 1, 1, 14, 17.29, -24.14, 1, 2, 13, 41.15, -11.67, 0.87368, 14, -27.58, -12.34, 0.12632, 1, 13, -3.75, -8.08, 1, 1, 13, -9.86, 3.51, 1], "hull": 10, "edges": [16, 18, 4, 6, 10, 4, 6, 8, 10, 8, 14, 0, 18, 0, 14, 16, 12, 14, 0, 2, 2, 4, 10, 12, 12, 2], "width": 21, "height": 73}}, "hoodtuzitou2": {"hoodtuzitou": {"x": 11.6, "y": 6.09, "scaleX": 2.5, "scaleY": 2.5, "rotation": -97.24, "width": 39, "height": 36}}, "hoodzui2": {"hoodzui": {"type": "mesh", "uvs": [1, 0.57155, 0.87814, 0.83173, 0.65414, 1, 0.39281, 1, 0.12081, 0.87764, 0, 0.66338, 0, 0.15834, 0.30748, 0.17364, 0.61681, 0, 1, 0, 0.29148, 0.51034, 0.59014, 0.50268, 0.80881, 0.44912], "triangles": [12, 8, 9, 11, 7, 8, 12, 11, 8, 10, 6, 7, 10, 7, 11, 12, 9, 0, 5, 6, 10, 1, 12, 0, 11, 12, 1, 4, 5, 10, 3, 10, 11, 2, 3, 11, 4, 10, 3, 1, 2, 11], "vertices": [0.96, -29.14, -11.01, -21.1, -18.75, -6.31, -18.75, 10.94, -13.12, 28.89, -3.26, 36.86, 19.97, 36.86, 19.27, 16.57, 27.25, -3.85, 27.25, -29.14, 3.78, 17.62, 4.13, -2.09, 6.59, -16.52], "hull": 10, "edges": [12, 14, 16, 18, 14, 16, 10, 12, 10, 8, 8, 6, 4, 6, 4, 2, 0, 18, 2, 0], "width": 30, "height": 21}}, "langertou2": {"langertou": {"type": "mesh", "uvs": [1, 0.56767, 0.78843, 0.72706, 0.5839, 1, 0.31832, 1, 0.15958, 0.75979, 0.00027, 0.57527, 0, 0.08169, 0.44422, 0.17674, 0.60778, 0.07026, 0.73723, 0.08067, 0.6748, 0.15193, 0.99999, 1e-05, 0.16018, 0.5147, 0.2475, 0.59967, 0.38755, 0.68512, 0.43024, 0.81533, 0.72571, 0.59967, 0.56858, 0.66885, 0.49343, 0.78481, 0.51564, 0.92316], "triangles": [12, 6, 7, 5, 6, 12, 13, 12, 7, 4, 12, 13, 4, 5, 12, 14, 13, 7, 17, 14, 7, 4, 13, 14, 18, 14, 17, 15, 14, 18, 1, 19, 18, 15, 18, 19, 15, 3, 4, 15, 4, 14, 3, 15, 19, 17, 1, 18, 2, 19, 1, 3, 19, 2, 10, 8, 9, 16, 10, 11, 16, 7, 10, 10, 7, 8, 7, 16, 17, 1, 16, 0, 17, 16, 1, 0, 16, 11], "vertices": [2, 115, 15, -158.82, 0.67448, 116, -24.49, -167.69, 0.32552, 3, 115, -43.26, -105.36, 0.75305, 116, -94.93, -131.77, 0.09651, 82, 35.46, 128.65, 0.15044, 2, 115, -143.6, -45.75, 0.10301, 82, 134.12, 66.3, 0.89699, 1, 82, 150.71, -33.24, 1, 3, 82, 85.04, -105.34, 0.44518, 117, -54.47, 98.57, 0.49794, 118, -82.79, 132.24, 0.05688, 2, 117, 25.25, 126.39, 0.65047, 118, 1.48, 126.94, 0.34953, 1, 118, 94.17, -0.98, 1, 4, 115, 103.99, 56.64, 0.07718, 116, 3.59, 63.73, 0.53529, 117, 63.52, -81.36, 0.26836, 118, -43.87, -79.37, 0.11917, 1, 116, 61.77, 23.33, 1, 1, 116, 78.39, -20.57, 1, 1, 116, 49.85, -11.1, 1, 2, 115, 199.77, -139.85, 0.09103, 116, 148.46, -99.95, 0.90897, 3, 82, 7.88, -117.97, 0.05882, 117, 15.36, 63.4, 0.70153, 118, -32.08, 72.73, 0.23965, 3, 82, 29.17, -80.79, 0.2777, 117, -23.73, 45.84, 0.67151, 118, -74.91, 71.71, 0.0508, 2, 82, 47.3, -23.81, 0.87528, 117, -71.91, 10.43, 0.12472, 1, 82, 85.61, -0.98, 1, 3, 115, -7.94, -74.18, 0.81594, 116, -69.25, -92.28, 0.11805, 82, -0.71, 98.46, 0.06601, 2, 115, -41.04, -19.8, 0.49414, 82, 30.87, 43.19, 0.50586, 2, 115, -82.81, 1.14, 0.06581, 82, 72.06, 21.11, 0.93419, 2, 115, -124.51, -15.59, 0.0586, 82, 114.2, 36.69, 0.9414], "hull": 12, "edges": [4, 6, 16, 18, 18, 20, 16, 20, 24, 26, 26, 28, 28, 30, 30, 6, 32, 34, 34, 36, 36, 38, 38, 4, 14, 16, 8, 10, 6, 8, 2, 4, 0, 2, 12, 14, 22, 0, 22, 32, 20, 22, 10, 12], "width": 171, "height": 144}}, "langshenti2": {"langshenti": {"type": "mesh", "uvs": [0.71657, 0.22816, 0.64341, 0.90537, 0.52664, 0.97794, 0.05829, 0.87837, 0, 0.34755, 0.18737, 0.66224], "triangles": [3, 5, 2, 2, 5, 1, 1, 5, 0, 3, 4, 5, 5, 4, 0], "vertices": [3, 80, 266.23, -112.47, 0.2741, 81, -139.31, 108.61, 0.29025, 79, -187.78, 242.52, 0.43566, 3, 80, -68.12, 389.69, 0.22246, 81, 278.49, 543.8, 0.32689, 79, -151.39, -359.66, 0.45065, 3, 80, 18.09, 500.03, 0.20004, 81, 404.9, 483.54, 0.51683, 79, -281.41, -411.67, 0.28313, 1, 81, 796.33, -31.13, 1, 1, 81, 348.17, -444.82, 1, 1, 81, 485.21, 23.61, 1], "hull": 5, "edges": [2, 4, 4, 6, 6, 8, 2, 0, 0, 8], "width": 516, "height": 352}}, "langshetou2": {"langshetou": {"type": "mesh", "uvs": [1, 0.54898, 0.90884, 0.80163, 0.63188, 0.99511, 0.46006, 0.95594, 0.21178, 0.58473, 0.08595, 0.84943, 0, 0.84943, 0, 0.41003, 0.13133, 0.11878, 0.2584, 0, 0.36731, 0, 0.64974, 0.1372, 0.9848, 0], "triangles": [4, 9, 10, 7, 8, 4, 4, 8, 9, 5, 6, 4, 6, 7, 4, 11, 2, 4, 11, 4, 10, 4, 2, 3, 11, 1, 2, 1, 11, 0, 11, 12, 0], "vertices": [1, 111, -7.72, 25.43, 1, 1, 111, 23.12, 40.27, 1, 2, 111, 91.17, 30.77, 0.39064, 112, -41.35, 21.02, 0.60936, 1, 112, -10.02, 36.32, 1, 3, 112, 65.69, 38.74, 0.19042, 114, 4.4, 29.55, 0.24196, 113, 16.46, 26.17, 0.56762, 1, 114, 43.18, 16.98, 1, 1, 114, 53.07, -0.83, 1, 1, 114, 19.85, -19.28, 1, 1, 113, 14.32, -26.3, 1, 2, 112, 89.66, -18.86, 0.22358, 113, -18.27, -25.65, 0.77642, 2, 112, 67.96, -32.84, 0.70384, 113, -41.92, -15.31, 0.29616, 2, 111, 49.06, -48.98, 0.56481, 112, 3.89, -56.99, 0.43519, 1, 111, -28.95, -28.29, 1], "hull": 13, "edges": [0, 24, 0, 2, 4, 6, 10, 12, 12, 14, 16, 18, 18, 20, 22, 4, 16, 8, 8, 10, 14, 16, 20, 22, 22, 24, 2, 4, 6, 8, 8, 18, 2, 22], "width": 107, "height": 47}}, "langshizhi2": {"langshizhi": {"type": "mesh", "uvs": [1, 0.26485, 0.69897, 0.47862, 0.27847, 1, 0, 1, 1e-05, 0.19223, 0.52332, 0, 1, 0], "triangles": [1, 2, 4, 2, 3, 4, 4, 5, 1, 1, 5, 0, 5, 6, 0], "vertices": [1, 88, -11.15, 38.64, 1, 2, 88, 60.9, 64.69, 0.47508, 89, 27.03, 65.67, 0.52492, 1, 89, 172.38, 36.56, 1, 1, 89, 191.32, -12.55, 1, 2, 88, 157.31, -48.8, 0.17, 89, 10.44, -82.31, 0.83, 1, 88, 48.31, -54.21, 1, 1, 88, -35.13, -20.22, 1], "hull": 7, "edges": [0, 12, 0, 2, 4, 6, 10, 12, 6, 8, 8, 2, 8, 10, 2, 4], "width": 85, "height": 108}}, "langshou-bei2": {"langshou-bei": {"type": "mesh", "uvs": [1, 0.20908, 0.64562, 0.58424, 0.58794, 0.83747, 0.43245, 0.84684, 0.43496, 1, 0.09032, 1, 0.07383, 0.66161, 0.32461, 0.62644, 0.34718, 0.44121, 0.64562, 0, 1, 0], "triangles": [5, 3, 4, 5, 6, 3, 6, 7, 3, 1, 3, 7, 2, 3, 1, 7, 8, 1, 8, 9, 1, 1, 9, 0, 9, 10, 0], "vertices": [1, 90, -25.74, 114.45, 1, 2, 90, 281.1, 89.87, 0.57, 91, 21.2, 88.01, 0.43, 2, 91, 179.97, 100.14, 0.67906, 92, -54.85, 91.67, 0.32094, 2, 92, 30.04, 63.04, 0.76507, 93, 11.44, 110.44, 0.23493, 2, 92, 64.49, 150.72, 0.26922, 93, 105.12, 120.4, 0.73078, 2, 92, 228.84, 105.2, 0.24805, 93, 140.69, -46.38, 0.75195, 2, 92, 177.51, -120.67, 0.82995, 93, -83.34, -105.24, 0.17005, 2, 91, 98.19, -81.74, 0.54178, 92, 35.9, -85.9, 0.45822, 2, 90, 313.69, -100.19, 0.52854, 91, -14.76, -101.43, 0.47146, 1, 90, -6.34, -125.71, 1, 1, 90, -128.61, 37.3, 1], "hull": 11, "edges": [0, 20, 0, 2, 4, 6, 6, 8, 18, 20, 18, 16, 16, 14, 14, 12, 2, 4, 10, 12, 12, 6, 8, 10], "width": 259, "height": 277}}, "langshou-qian2": {"langshou-qian": {"type": "mesh", "uvs": [1, 0.27417, 0.62026, 0.73473, 0.57944, 0.94041, 0.47942, 1, 0.24469, 1, 0.16713, 0.87888, 0, 0.94568, 0, 0.78395, 0.13038, 0.62575, 0.34675, 0.59059, 0.53861, 0.00176, 1, 0], "triangles": [10, 11, 0, 9, 10, 0, 1, 9, 0, 5, 8, 9, 7, 8, 5, 1, 3, 9, 3, 5, 9, 6, 7, 5, 3, 4, 5, 3, 1, 2], "vertices": [1, 87, -356.91, 121.01, 1, 1, 87, 216.94, 128.69, 1, 1, 87, 406.75, 223.1, 1, 1, 87, 507.26, 188.15, 1, 1, 87, 628.41, 22.29, 1, 1, 87, 569.07, -105.1, 1, 1, 87, 710.13, -183.15, 1, 1, 87, 577.44, -280.07, 1, 1, 87, 380.35, -282.75, 1, 1, 87, 239.84, -150.95, 1, 1, 87, -342.28, -368.25, 1, 1, 87, -581.85, -43.3, 1], "hull": 12, "edges": [0, 22, 2, 4, 4, 6, 20, 22, 18, 16, 16, 14, 6, 8, 8, 10, 12, 14, 10, 12, 18, 20, 0, 2], "width": 197, "height": 229}}, "langshou-you2": {"langshou-you": {"x": 174.08, "y": 12.19, "scaleX": 5, "scaleY": 5, "rotation": 43.9, "width": 116, "height": 112}}, "langshouzhi-qian2": {"langshouzhi-qian": {"type": "mesh", "uvs": [1, 0.44248, 1, 0.61355, 0.89008, 1, 0.69624, 0.99999, 0.63946, 0.68634, 0.6918, 0.46496, 0.37314, 0.41529, 0.07225, 0.78023, 1e-05, 0.24826, 0.20178, 0.00679, 0.79381, 0], "triangles": [7, 8, 6, 8, 9, 6, 6, 9, 10, 1, 2, 4, 2, 3, 4, 4, 5, 1, 5, 0, 1, 0, 5, 10, 5, 6, 10], "vertices": [2, 94, 97.49, 52.11, 0.85693, 95, -47.58, 21.35, 0.14307, 2, 94, 136.46, 29.3, 0.24793, 95, -11.74, 48.82, 0.75207, 1, 95, 95.11, 77.14, 1, 1, 95, 140.75, 17.6, 1, 1, 95, 88.41, -50.21, 1, 3, 94, 42.35, -53.82, 0.38011, 95, 29.7, -69.7, 0.25603, 92, 6.3, 55.43, 0.36387, 2, 92, 115.4, -3.55, 0.65, 93, -8.17, 3.97, 0.35, 1, 93, 81.42, -107.69, 1, 1, 93, -39.01, -143.83, 1, 2, 92, 121.6, -140.86, 0.65, 93, -127.07, -64.99, 0.35, 2, 94, -43.64, 42.26, 0.89224, 92, -76.85, -43.12, 0.10776], "hull": 11, "edges": [10, 12, 10, 8, 8, 6, 20, 0, 10, 0, 0, 2, 10, 2, 4, 6, 2, 4, 18, 20, 18, 12, 14, 16, 10, 20, 16, 18, 12, 14], "width": 174, "height": 119}}, "langweiba2": {"langweiba": {"type": "mesh", "uvs": [0.72612, 0.94542, 0, 0.89006, 0.00632, 0.54332, 0.14008, 0.58402, 0.22672, 0.70448, 0.52997, 0.70123, 0.6825, 0.57751, 0.7774, 0.24166], "triangles": [1, 2, 3, 1, 3, 4, 1, 4, 0, 0, 6, 7, 5, 6, 0, 4, 5, 0], "vertices": [2, 83, -349.74, 90.5, 0.29822, 87, 484.06, 173, 0.70178, 1, 85, 80.82, 220.36, 1, 1, 85, 379.4, -3.11, 1, 2, 85, 195.02, -190.7, 0.84767, 84, 234.16, -271.39, 0.15233, 3, 83, 602.73, -221.82, 0.0875, 85, -7.35, -254.29, 0.17345, 84, 38.1, -190.42, 0.73905, 2, 83, 15.45, -192.7, 0.85569, 87, 496.41, -288.96, 0.14431, 1, 87, 214.57, -128.59, 1, 1, 87, -185.52, -192.94, 1], "hull": 8, "edges": [2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 0, 0, 2, 8, 10], "width": 437, "height": 242}}, "langxiaer2": {"langxiaer": {"type": "mesh", "uvs": [1, 0.31772, 0.62428, 0.2245, 0.59296, 0.61912, 0.50595, 0.83663, 0.34584, 1, 0.22165, 1, 0.13464, 0.81756, 0.06474, 0.6097, 0, 0.4425, 0, 0, 1, 0], "triangles": [3, 4, 6, 4, 5, 6, 3, 6, 2, 6, 7, 2, 1, 2, 8, 2, 7, 8, 8, 9, 1, 1, 10, 0, 1, 9, 10], "vertices": [1, 108, 10.73, 209.27, 1, 1, 108, 4.89, 69.79, 1, 2, 108, 161.54, 94.82, 0.54053, 109, -5.87, 97.6, 0.45947, 2, 109, 80.09, 63.12, 0.71098, 110, -33.48, 65.75, 0.28902, 1, 110, 44.46, 26.99, 1, 1, 110, 55.78, -16.01, 1, 2, 109, 67.34, -69.41, 0.52813, 110, -7.05, -64.75, 0.47187, 2, 108, 200.83, -90.19, 0.66086, 109, -16.91, -91.22, 0.33914, 1, 108, 140.81, -127.99, 1, 1, 108, -31.99, -168.31, 1, 1, 108, -113.34, 180.32, 1], "hull": 11, "edges": [18, 20, 0, 20, 2, 4, 16, 18, 14, 16, 8, 10, 10, 12, 6, 8, 4, 6, 12, 14, 0, 2], "width": 161, "height": 180}}, "liuhai2": {"liuhai": {"type": "mesh", "uvs": [0.86598, 0.17849, 1, 0.42014, 1, 0.73924, 0.89307, 0.91879, 0.60169, 1, 0.31652, 0.93275, 0.12519, 1, 0, 0.76538, 1e-05, 0.45489, 0.08662, 0.08481, 0.39119, 1e-05, 0.64352, 1e-05, 0.68393, 0.70534, 0.62239, 0.31339], "triangles": [13, 10, 11, 13, 9, 10, 9, 13, 8, 12, 5, 8, 12, 8, 13, 6, 7, 5, 7, 8, 5, 4, 12, 3, 4, 5, 12, 3, 12, 2, 12, 1, 2, 1, 12, 13, 1, 13, 0, 13, 11, 0], "vertices": [2, 54, 55.26, 59.49, 0.8481, 55, -66.97, 49.81, 0.1519, 2, 54, 126.34, 47.43, 0.13844, 55, 2.53, 68.98, 0.86156, 2, 55, 94.72, 52.65, 0.40533, 56, -23.41, 55.19, 0.59467, 1, 56, 31.84, 40.29, 1, 2, 56, 65.88, -36.22, 0.79568, 57, 69.01, 100.59, 0.20432, 2, 56, 69.22, -107.9, 0.13784, 57, 54.53, 30.31, 0.86216, 1, 57, 72.15, -16.69, 1, 2, 57, 5.65, -49.02, 0.82767, 52, 95.17, -47.75, 0.17233, 2, 52, 7.01, -50.93, 0.92074, 51, 105.71, -47.75, 0.07926, 1, 51, 19.75, -39.5, 1, 2, 51, -33.68, 40.4, 0.05137, 53, -17.45, 10.12, 0.94863, 2, 54, -20.14, 41.53, 0.39283, 53, 38.97, 46.02, 0.60717, 2, 55, 69.57, -26.65, 0.4815, 56, -14.48, -27.53, 0.5185, 2, 54, 47.83, -15.32, 0.8915, 55, -42.03, -21.11, 0.1085], "hull": 12, "edges": [12, 10, 20, 22, 16, 18, 14, 16, 12, 14, 22, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 24, 26, 18, 20], "width": 119, "height": 127}}, "root": {"root": {"type": "clipping", "end": "root", "vertexCount": 4, "vertices": [-488.17, 794.24, -488.18, -174.25, 1028, -174.22, 1028.01, 794.32], "color": "ce3a3aff"}}, "sedai-qian2": {"sedai-qian": {"type": "mesh", "uvs": [0.66958, 0.13158, 1, 0.11418, 1, 0.61873, 0.76263, 0.63265, 0.31809, 0.94582, 0.1258, 0.99999, 0, 0, 0.30568, 0], "triangles": [3, 4, 7, 4, 5, 6, 4, 6, 7, 3, 0, 1, 3, 7, 0, 2, 3, 1], "vertices": [2, 29, 26.84, 39.32, 0.07735, 30, 15.3, -33.91, 0.92265, 1, 29, 32.89, -28.84, 1, 1, 29, -29.07, -32.39, 1, 2, 29, -33.59, 16.56, 0.17981, 30, -2.89, 28.05, 0.82019, 2, 30, 89.79, 64.97, 0.17445, 31, -15.43, 67.51, 0.82555, 1, 31, 21.59, 83.59, 1, 1, 31, 65.77, -32.16, 1, 2, 30, 90.34, -51.39, 0.09565, 31, 15.17, -44.76, 0.90435], "hull": 8, "edges": [12, 14, 2, 4, 4, 6, 8, 10, 0, 2, 10, 12, 14, 0, 6, 8], "width": 93, "height": 55}}, "texiao-guang2": {"texiao-guang": {"x": -70.58, "y": -83.6, "scaleX": 5, "scaleY": 5, "rotation": -88.52, "width": 419, "height": 294}}, "texiaoixiantiaoguang2": {"texiaoixiantiaoguang": {"x": 730.59, "y": 30.46, "scaleX": 5, "scaleY": 5, "rotation": 403.54, "width": 368, "height": 276}}, "texiaoixiantiaoguang3": {"texiaoixiantiaoguang": {"x": 728.94, "y": 27.34, "scaleX": 5, "scaleY": 5, "rotation": 43.54, "width": 368, "height": 276}}}}], "animations": {"hood_win": {"slots": {"bone154": {"attachment": [{}]}, "bone155": {"attachment": [{}]}, "bone156": {"attachment": [{}]}, "bone157": {"attachment": [{}]}, "bone158": {"attachment": [{}]}, "bone159": {"attachment": [{}]}, "bone160": {"attachment": [{}]}, "bone161": {"attachment": [{}]}, "langertou2": {"attachment": [{}]}, "langshenti2": {"attachment": [{}]}, "langshetou2": {"attachment": [{}]}, "langshizhi2": {"attachment": [{}]}, "langshou-bei2": {"attachment": [{}]}, "langshou-qian2": {"attachment": [{}]}, "langshou-you2": {"attachment": [{}]}, "langshouzhi-qian2": {"attachment": [{}]}, "langweiba2": {"attachment": [{}]}, "langxiaer2": {"attachment": [{}]}, "texiao-guang2": {"rgba": [{"color": "ffffffc7", "curve": [0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 0.9, 0.148, 1]}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff66", "curve": [0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 0.4, 0.903, 0.61]}, {"time": 1, "color": "ffffffc7"}]}, "texiaoixiantiaoguang2": {"rgba": [{"color": "ffffff5d", "curve": [0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 0.16, 0.148, 0]}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": [0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 1, 0.903, 0.64]}, {"time": 1, "color": "ffffff5d"}]}, "texiaoixiantiaoguang3": {"rgba": [{"color": "ffffffdd", "curve": [0.128, 1, 0.303, 1, 0.128, 1, 0.303, 1, 0.128, 1, 0.303, 1, 0.128, 0.62, 0.303, 0]}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": [0.952, 1, 0.975, 1, 0.952, 1, 0.975, 1, 0.952, 1, 0.975, 1, 0.952, 1, 0.975, 0.95]}, {"time": 1, "color": "ffffffdd"}]}}, "bones": {"bone16": {"rotate": [{"value": -0.34, "curve": [0.102, 0.35, 0.195, 0.83]}, {"time": 0.2667, "value": 0.83, "curve": [0.296, -1.2, 0.392, -5.94]}, {"time": 0.4333, "value": -5.94, "curve": [0.589, -5.34, 0.791, -3.91]}, {"time": 0.9667, "value": -2.96}]}, "bone": {"translate": [{"x": 1711.02, "curve": [0.015, 691.78, 0.078, 106.97, 0.015, 0, 0.078, 0]}, {"time": 0.2, "x": 40.13, "curve": [0.346, 30.9, 0.8, 0, 0.346, 0, 0.8, 0]}, {"time": 1}], "scale": [{"x": 1.641, "curve": [0.047, 1.641, 0.145, 0.98, 0.047, 1, 0.145, 1.01]}, {"time": 0.1667, "x": 0.98, "y": 1.01, "curve": [0.25, 0.98, 0.417, 1, 0.25, 1.01, 0.417, 1]}, {"time": 0.5}]}, "bone2": {"translate": [{"y": -78.65, "curve": [0.015, 0, 0.078, 0, 0.015, -41.73, 0.078, -20.55]}, {"time": 0.2, "y": -18.13, "curve": [0.791, 0, 0.802, 0, 0.791, -18.13, 0.802, 0]}, {"time": 1}], "scale": [{"x": 0.9, "y": 0.9, "curve": [0.058, 0.9, 0.175, 1.02, 0.058, 0.9, 0.175, 1.02]}, {"time": 0.2333, "x": 1.02, "y": 1.02, "curve": [0.425, 1.02, 0.808, 1, 0.425, 1.02, 0.808, 1]}, {"time": 1}]}, "bone3": {"rotate": [{"value": 1.75, "curve": [0.088, 2.69, 0.169, 3.32]}, {"time": 0.2333, "value": 3.32, "curve": [0.411, 3.32, 0.724, 1.31]}, {"time": 0.9667, "value": 0.37}], "translate": [{"curve": [0.012, 1.51, 0.065, 2.37, 0.012, 6.47, 0.065, 10.18]}, {"time": 0.1667, "x": 2.47, "y": 10.6, "curve": [0.375, 2.47, 0.792, 0, 0.375, 10.6, 0.792, 0]}, {"time": 1}]}, "bone4": {"rotate": [{"value": -5.98, "curve": [0.058, -5.98, 0.175, 1.17]}, {"time": 0.2333, "value": 1.17, "curve": [0.614, 1.17, 0.783, -1.11]}, {"time": 0.9667, "value": -1.11}]}, "bone5": {"rotate": [{"value": 0.89, "curve": [0.14, 2.55, 0.27, 3.8]}, {"time": 0.3667, "value": 3.8, "curve": [0.512, 3.8, 0.756, -0.38]}, {"time": 0.9667, "value": -3.16}]}, "bone10": {"rotate": [{"value": 4.23, "curve": [0.248, 1.13, 0.556, -4.62]}, {"time": 0.7333, "value": -4.62, "curve": [0.798, -4.62, 0.878, -3.8]}, {"time": 0.9667, "value": -2.56}]}, "bone15": {"rotate": [{}, {"time": 0.2667, "value": -6.76, "curve": [0.442, -6.76, 0.792, 0]}, {"time": 0.9667}]}, "bone17": {"rotate": [{"value": -6.78, "curve": [0.242, -6.78, 0.725, 0]}, {"time": 0.9667}]}, "bone18": {"rotate": [{"value": -9.93, "curve": [0.113, -0.74, 0.224, 7.74]}, {"time": 0.3, "value": 7.74, "curve": [0.465, 7.74, 0.719, 4.57]}, {"time": 0.9667, "value": 1.64}]}, "bone19": {"rotate": [{"value": 9.12, "curve": [0.103, 9.12, 0.248, 11.25]}, {"time": 0.4, "value": 13.86, "curve": [0.606, 7.06, 0.828, -1.59]}, {"time": 0.9667, "value": -1.59}]}, "bone20": {"translate": [{"curve": [0.012, 3.96, 0.065, 6.23, 0.012, 0.92, 0.065, 1.45]}, {"time": 0.1667, "x": 6.49, "y": 1.51, "curve": [0.375, 6.49, 0.792, 0, 0.375, 1.51, 0.792, 0]}, {"time": 1}]}, "bone21": {"rotate": [{"value": 3.82, "curve": [0.245, 1.92, 0.58, -2.72]}, {"time": 0.7667, "value": -2.72, "curve": [0.823, -2.72, 0.892, -2.35]}, {"time": 0.9667, "value": -1.75}]}, "bone23": {"rotate": [{"value": 1.18, "curve": [0.049, 1.52, 0.093, 1.71]}, {"time": 0.1333, "value": 1.71, "curve": [0.337, 1.71, 0.711, -4.13]}, {"time": 0.9667, "value": -5.97}]}, "bone25": {"rotate": [{"value": 2.04, "curve": [0.164, 6.32, 0.324, 10.27]}, {"time": 0.4333, "value": 10.27, "curve": [0.566, 10.27, 0.769, 4.98]}, {"time": 0.9667, "value": 0.09}]}, "bone26": {"rotate": [{"value": -15.93, "curve": [0.245, -10.94, 0.58, 1.29]}, {"time": 0.7667, "value": 1.29, "curve": [0.823, 1.29, 0.892, 0.31]}, {"time": 0.9667, "value": -1.29}]}, "bone28": {"rotate": [{"value": -3.15, "curve": [0.075, -4.09, 0.143, -4.67]}, {"time": 0.2, "value": -4.67, "curve": [0.386, -4.67, 0.721, 2.55]}, {"time": 0.9667, "value": 5.5}]}, "bone30": {"rotate": [{"value": -2.96, "curve": [0.14, -8.77, 0.272, -13.71]}, {"time": 0.3667, "value": -13.71, "curve": [0.514, -13.71, 0.746, -4.63]}, {"time": 0.9667, "value": 3.11}]}, "bone31": {"rotate": [{"value": 6.91, "curve": [0.101, -4.66, 0.194, -12.7]}, {"time": 0.2667, "value": -12.7, "curve": [0.436, -12.7, 0.734, -3.54]}, {"time": 0.9667, "value": 0.97}], "translate": [{"curve": [0.012, -3.07, 0.065, -4.83, 0.012, -6.8, 0.065, -10.69]}, {"time": 0.1667, "x": -5.03, "y": -11.14, "curve": [0.255, -5.93, 0.336, -6.55, 0.255, -11.61, 0.336, -11.94]}, {"time": 0.4, "x": -6.55, "y": -11.94, "curve": [0.545, -6.55, 0.8, -2.49, 0.545, -11.94, 0.8, -9.82]}, {"time": 1, "x": -0.49, "y": -8.78}]}, "bone32": {"rotate": [{"value": -3.75, "curve": [0.242, -3.75, 0.725, 0]}, {"time": 0.9667}]}, "bone34": {"rotate": [{"value": 0.88, "curve": [0.225, -2.09, 0.45, -5.06]}, {"time": 0.6, "value": -5.06, "curve": [0.692, -5.06, 0.829, -2.09]}, {"time": 0.9667, "value": 0.88}]}, "bone36": {"rotate": [{"value": 3.43, "curve": [0.184, 0.64, 0.377, -2.5]}, {"time": 0.5, "value": -2.5, "curve": [0.619, -2.5, 0.79, -0.47]}, {"time": 0.9667, "value": 1.81}]}, "bone38": {"rotate": [{"value": 0.83, "curve": [0.075, 1.69, 0.143, 2.22]}, {"time": 0.2, "value": 2.22, "curve": [0.386, 2.22, 0.721, -4.38]}, {"time": 0.9667, "value": -7.07}], "translate": [{"curve": [0.012, 2.04, 0.065, 3.21, 0.012, -1.31, 0.065, -2.06]}, {"time": 0.1667, "x": 3.34, "y": -2.15, "curve": [0.229, 4.07, 0.286, 4.52, 0.229, -1.91, 0.286, -1.77]}, {"time": 0.3333, "x": 4.52, "y": -1.77, "curve": [0.495, 4.52, 0.787, -1.1, 0.495, -1.77, 0.787, -3.58]}, {"time": 1, "x": -3.39, "y": -4.32}]}, "bone41": {"rotate": [{"value": 1.8, "curve": [0.228, 5.12, 0.447, 8.07]}, {"time": 0.6, "value": 8.07, "curve": [0.689, 8.07, 0.838, 1.43]}, {"time": 0.9667, "value": -3}]}, "bone43": {"rotate": [{"value": -21.04, "curve": [0.242, -21.04, 0.725, 0]}, {"time": 0.9667}]}, "bone44": {"rotate": [{"value": 5.26, "curve": [0.064, 2.21, 0.122, 0]}, {"time": 0.1667, "curve": [0.283, 0, 0.517, 20.88]}, {"time": 0.6333, "value": 20.88, "curve": [0.714, 20.88, 0.851, 11.2]}, {"time": 0.9667, "value": 5.26}]}, "bone46": {"rotate": [{"value": -6.54, "curve": [0.025, -4.9, 0.05, -3.27]}, {"time": 0.0667, "value": -3.27, "curve": [0.092, -3.27, 0.129, -1.63]}, {"time": 0.1667, "curve": [0.283, 0, 0.517, -25.97]}, {"time": 0.6333, "value": -25.97, "curve": [0.758, -16.25, 0.883, -6.54]}, {"time": 0.9667, "value": -6.54}]}, "bone48": {"rotate": [{"value": -19.86, "curve": [0.105, -8.73, 0.227, 7.96]}, {"time": 0.3, "value": 7.96, "curve": [0.433, 7.96, 0.7, -30.88]}, {"time": 0.8333, "value": -30.88, "curve": [0.869, -30.88, 0.916, -26.14]}, {"time": 0.9667, "value": -19.86}]}, "bone51": {"rotate": [{"value": -1.24, "curve": [0.14, -3.17, 0.27, -4.62]}, {"time": 0.3667, "value": -4.62, "curve": [0.512, -4.62, 0.756, 0.22]}, {"time": 0.9667, "value": 3.45}]}, "bone53": {"rotate": [{"value": 3.92, "curve": [0.239, -0.13, 0.504, -5.5]}, {"time": 0.6667, "value": -5.5, "curve": [0.746, -5.5, 0.852, -3.68]}, {"time": 0.9667, "value": -1.26}]}, "bone54": {"rotate": [{"value": 0.57, "curve": [0.165, 2.06, 0.322, 3.33]}, {"time": 0.4333, "value": 3.33, "curve": [0.565, 3.33, 0.772, 1.01]}, {"time": 0.9667, "value": -0.89}]}, "bone56": {"rotate": [{"value": -4.2, "curve": [0.248, -0.08, 0.556, 7.58]}, {"time": 0.7333, "value": 7.58, "curve": [0.798, 7.58, 0.878, 6.49]}, {"time": 0.9667, "value": 4.85}]}, "bone57": {"rotate": [{"value": 11.02, "curve": [0.242, 11.02, 0.725, 0]}, {"time": 0.9667}]}, "bone59": {"rotate": [{"value": -0.78, "curve": [0.189, 2.62, 0.374, 5.76]}, {"time": 0.5, "value": 5.76, "curve": [0.615, 5.76, 0.794, 1.55]}, {"time": 0.9667, "value": -2.33}]}, "bone61": {"rotate": [{"value": -5.51, "curve": [0.229, 0.95, 0.505, 11.95]}, {"time": 0.6667, "value": 11.95, "curve": [0.747, 11.95, 0.852, 9.54]}, {"time": 0.9667, "value": 6.22}]}, "bone62": {"rotate": [{"value": 1.18, "curve": [0.101, 1.89, 0.194, 2.38]}, {"time": 0.2667, "value": 2.38, "curve": [0.436, 2.38, 0.734, -0.8]}, {"time": 0.9667, "value": -2.36}]}, "bone64": {"rotate": [{"value": 2.12, "curve": [0.218, 3.03, 0.453, 4.19]}, {"time": 0.6, "value": 4.19, "curve": [0.695, 4.19, 0.827, 2.85]}, {"time": 0.9667, "value": 1.21}]}, "bone66": {"rotate": [{"value": -7.01, "curve": [0.242, -7.01, 0.725, 0]}, {"time": 0.9667}]}, "bone68": {"rotate": [{"value": -5.33, "curve": [0.242, -5.33, 0.725, 0]}, {"time": 0.9667}]}, "bone70": {"rotate": [{"value": -2.07, "curve": [0.14, -3.63, 0.27, -4.81]}, {"time": 0.3667, "value": -4.81, "curve": [0.512, -4.81, 0.756, -0.89]}, {"time": 0.9667, "value": 1.73}]}, "bone72": {"rotate": [{"value": 6.34, "curve": [0.049, 6.99, 0.093, 7.35]}, {"time": 0.1333, "value": 7.35, "curve": [0.337, 7.35, 0.711, -3.82]}, {"time": 0.9667, "value": -7.35}]}, "bone73": {"rotate": [{"value": 1.31}]}, "bone75": {"rotate": [{"value": 3.94, "curve": [0.242, 3.94, 0.725, 0]}, {"time": 0.9667}]}, "bone77": {"rotate": [{"value": 6.46, "curve": [0.14, 9.98, 0.271, 12.75]}, {"time": 0.3667, "value": 12.75, "curve": [0.513, 12.75, 0.753, 4.89]}, {"time": 0.9667, "value": -0.8}]}, "bone79": {"rotate": [{"value": 12.95, "curve": [0.242, 12.95, 0.725, 0]}, {"time": 0.9667}]}, "bone83": {"rotate": [{"value": -1.23, "curve": [0.218, 1.99, 0.453, 6.09]}, {"time": 0.6, "value": 6.09, "curve": [0.695, 6.09, 0.827, 4.17]}, {"time": 0.9667, "value": 1.83}], "translate": [{"curve": [0.012, 0.13, 0.065, 0.21, 0.012, 0.63, 0.065, 0.99]}, {"time": 0.1667, "x": 0.22, "y": 1.03, "curve": [0.348, 0.75, 0.544, 1.42, 0.348, -2.38, 0.544, -6.73]}, {"time": 0.6667, "x": 1.42, "y": -6.73, "curve": [0.753, 1.42, 0.873, 1.11, 0.753, -6.73, 0.873, -4.7]}, {"time": 1, "x": 0.72, "y": -2.22}]}, "bone87": {"rotate": [{"curve": [0.117, 0, 0.35, -30.7]}, {"time": 0.4667, "value": -30.7, "curve": [0.592, -30.7, 0.842, 0]}, {"time": 0.9667}]}, "bone89": {"rotate": [{"value": 23.28}, {"time": 0.0667, "value": 26.24, "curve": [0.158, 26.24, 0.342, 24.03]}, {"time": 0.4333, "value": 24.03, "curve": [0.558, 24.03, 0.808, 0]}, {"time": 0.9333}, {"time": 0.9667, "value": 1.48}]}, "bone92": {"rotate": [{"curve": [0.117, 0, 0.35, 16.11]}, {"time": 0.4667, "value": 16.11, "curve": [0.592, 16.11, 0.842, 0]}, {"time": 0.9667}]}, "bone99": {"rotate": [{"value": -3.81, "curve": [0.242, -3.81, 0.725, 0]}, {"time": 0.9667}]}, "bone100": {"rotate": [{"value": -0.78, "curve": [0.101, -1.42, 0.193, -1.85]}, {"time": 0.2667, "value": -1.85, "curve": [0.437, -1.85, 0.746, 1.92]}, {"time": 0.9667, "value": 3.32}]}, "bone102": {"rotate": [{"value": 1.36, "curve": [0.221, -1.1, 0.452, -3.88]}, {"time": 0.6, "value": -3.88, "curve": [0.694, -3.88, 0.827, -2.1]}, {"time": 0.9667, "value": -0.1}]}, "bone104": {"rotate": [{"value": 7.25, "curve": [0.242, 7.25, 0.725, 0]}, {"time": 0.9667}]}, "bone95": {"rotate": [{"value": -1.22, "curve": [0.218, -0.05, 0.453, 1.43]}, {"time": 0.6, "value": 1.43, "curve": [0.695, 1.43, 0.827, 0.74]}, {"time": 0.9667, "value": -0.11}]}, "bone97": {"rotate": [{"value": -3.17, "curve": [0.242, -3.17, 0.725, 0]}, {"time": 0.9667}]}, "bone105": {"rotate": [{"value": -2.43, "curve": [0.102, -4.61, 0.195, -6.13]}, {"time": 0.2667, "value": -6.13, "curve": [0.437, -6.13, 0.724, 1.27]}, {"time": 0.9667, "value": 5.8}]}, "bone106": {"scale": [{}, {"time": 0.1667, "x": 1.043, "y": 1.052, "curve": [0.375, 1.043, 0.792, 1, 0.375, 1.052, 0.792, 1]}, {"time": 1}]}, "bone107": {"rotate": [{"value": 1.49, "curve": [0.218, -0.46, 0.453, -2.94]}, {"time": 0.6, "value": -2.94, "curve": [0.695, -2.94, 0.827, -1.78]}, {"time": 0.9667, "value": -0.36}]}, "bone109": {"rotate": [{"value": 9.22, "curve": [0.242, 9.22, 0.725, 4.07]}, {"time": 0.9667, "value": 4.07}]}, "bone111": {"rotate": [{"value": -18.62, "curve": [0.102, -17.74, 0.195, -17.12]}, {"time": 0.2667, "value": -17.12, "curve": [0.437, -17.12, 0.724, -16.42]}, {"time": 0.9667, "value": -15.99}]}, "bone121": {"rotate": [{"value": -0.82, "curve": [0.164, 1.09, 0.324, 2.86]}, {"time": 0.4333, "value": 2.86, "curve": [0.566, 2.86, 0.769, 0.49]}, {"time": 0.9667, "value": -1.7}]}, "bone123": {"rotate": [{"value": -11.03, "curve": [0.242, -11.03, 0.725, 0]}, {"time": 0.9667}]}, "bone124": {"rotate": [{"value": -6.85, "curve": [0.242, -6.85, 0.725, 0]}, {"time": 0.9667}]}, "bone126": {"rotate": [{"value": -0.77, "curve": [0.19, 2.89, 0.373, 6.13]}, {"time": 0.5, "value": 6.13, "curve": [0.614, 6.13, 0.797, 0.2]}, {"time": 0.9667, "value": -4.46}]}, "bone127": {"rotate": [{"value": -4.64, "curve": [0.228, 0.73, 0.478, 7.84]}, {"time": 0.6333, "value": 7.84, "curve": [0.72, 7.84, 0.839, 5.02]}, {"time": 0.9667, "value": 1.42}]}, "bone136": {"rotate": [{"value": 0.32, "curve": [0.188, 1.93, 0.375, 3.55]}, {"time": 0.5, "value": 3.55, "curve": [0.617, 3.55, 0.792, 1.93]}, {"time": 0.9667, "value": 0.32}]}, "bone138": {"rotate": [{"value": -4.75, "curve": [0.242, -4.75, 0.725, 0]}, {"time": 0.9667}]}, "bone140": {"rotate": [{"value": 0.25, "curve": [0.191, -3.83, 0.372, -7.3]}, {"time": 0.5, "value": -7.3, "curve": [0.613, -7.3, 0.803, 1.33]}, {"time": 0.9667, "value": 7.08}]}, "bone141": {"rotate": [{"value": 4.2, "curve": [0.242, 4.2, 0.725, 0]}, {"time": 0.9667}]}, "bone143": {"rotate": [{"value": 0.54, "curve": [0.166, 1.94, 0.322, 3.09]}, {"time": 0.4333, "value": 3.09, "curve": [0.563, 3.09, 0.776, 0.29]}, {"time": 0.9667, "value": -1.73}]}, "bone145": {"rotate": [{"value": -3.11, "curve": [0.229, 1.58, 0.505, 9.56]}, {"time": 0.6667, "value": 9.56, "curve": [0.747, 9.56, 0.852, 7.82]}, {"time": 0.9667, "value": 5.41}]}, "bone146": {"rotate": [{"value": 14.62, "curve": [0.242, 14.62, 0.725, 0]}, {"time": 0.9667}], "translate": [{"curve": [0.012, 2.68, 0.065, 4.22, 0.012, 3.08, 0.065, 4.85]}, {"time": 0.1667, "x": 4.4, "y": 5.05, "curve": [0.375, 4.4, 0.792, 0, 0.375, 5.05, 0.792, 0]}, {"time": 1}]}, "bone147": {"rotate": [{"value": -17.57, "curve": [0.242, -17.57, 0.725, 0]}, {"time": 0.9667}]}, "bone148": {"rotate": [{"value": -3.94, "curve": [0.239, 3.93, 0.504, 14.36]}, {"time": 0.6667, "value": 14.36, "curve": [0.746, 14.36, 0.852, 10.81]}, {"time": 0.9667, "value": 6.11}], "translate": [{"curve": [0.012, 1.34, 0.065, 2.11, 0.012, 0.34, 0.065, 0.53]}, {"time": 0.1667, "x": 2.2, "y": 0.55, "curve": [0.37, 1.25, 0.595, 0, 0.37, -0.61, 0.595, -2.14]}, {"time": 0.7333, "y": -2.14, "curve": [0.803, 0, 0.898, 0.43, 0.803, -2.14, 0.898, -1.62]}, {"time": 1, "x": 0.99, "y": -0.93}]}, "bone149": {"rotate": [{"value": -6.57, "curve": [0.101, -9.3, 0.194, -11.19]}, {"time": 0.2667, "value": -11.19, "curve": [0.436, -11.19, 0.734, 1.1]}, {"time": 0.9667, "value": 7.16}]}, "bone150": {"translate": [{"x": 1754.76}]}, "bone216": {"translate": [{"x": -37.66, "curve": [0.022, -20.43, 0.117, -10.54, 0.022, 0, 0.117, 0]}, {"time": 0.3, "x": -9.41, "curve": [0.348, -4.14, 0.825, 0, 0.348, 0, 0.825, 0]}, {"time": 1}]}, "bone217": {"translate": [{"x": -54.95, "y": 1.05, "curve": [0.024, -27.62, 0.13, -11.94, 0.024, 0.53, 0.13, 0.22]}, {"time": 0.3333, "x": -10.15, "y": 0.19, "curve": [0.435, -4.36, 0.833, 0, 0.435, 0.08, 0.833, 0]}, {"time": 1}]}, "bone219": {"translate": [{"x": 59.47, "y": -1.86, "curve": [0.019, 30.6, 0.104, 14.04, 0.019, -0.96, 0.104, -0.44]}, {"time": 0.2667, "x": 12.15, "y": -0.38, "curve": [0.432, 8.63, 0.817, 0, 0.432, -0.27, 0.817, 0]}, {"time": 1}]}, "bone221": {"translate": [{"x": 34.37, "y": -0.73, "curve": [0.029, 26.8, 0.156, 22.46, 0.029, -0.57, 0.156, -0.48]}, {"time": 0.4, "x": 21.96, "y": -0.47, "curve": [0.535, 21.13, 0.85, 18.75, 0.535, -0.45, 0.85, -0.4]}, {"time": 1, "x": 18.75, "y": -0.4}]}}, "attachments": {"default": {"bone9": {"hoodlian-ying": {"deform": [{"offset": 2, "vertices": [-13.07286, -25.77524]}, {"time": 0.5667, "offset": 2, "vertices": [-13.8064, 32.08969], "curve": [0.675, 0, 0.892, 1]}, {"time": 1, "offset": 2, "vertices": [-13.07286, -25.77524]}]}}, "bone32": {"hoodlanzibu": {"deform": [{"time": 0.7, "vertices": [-0.27623, 3.58497, -5.95604, -7.29252, -0.44654, -1.74286, 3.50201, -4.99654, 5.39078, 2.85828, 4.10086, 4.51801, -6.43421, 2.72496, -6.8168, 0.46538, -0.38973, 5.24955, 1.00252, 5.13037, 1.12475, 8.67104, 1.50613, 4.01571, 3.4529, -1.24128, 0.95257, 3.54652, -1.34632, 6.99455, -0.43366, 2.81259, 0.81804, 1.60275, -1.56064, 0.89607, 0, 0, 0.40012, 4.039, 0.07462, 4.0581, 3.04968, 8.96557, 1.7886, 4.8804], "curve": [0.776, 0, 0.887, 0.49]}, {"time": 1, "vertices": [-1.82899, -0.17451, -6.62571, -7.71366, -0.87929, -3.38304, 1.87279, -2.67202, 2.88285, 1.52853, 2.19304, 2.41611, -2.91226, -3.26755, -1.21402, -3.83659, -1.24361, 1.05683, -0.80703, 1.21658, -0.98668, 1.50434, 0.88401, 3.24588, 4.77047, -2.04325, 1.0855, 5.07791, -1.27558, 1.38702, -0.23193, 1.5041, 0.43747, 0.85711, -0.83459, 0.4792, 0, 0, 0.21397, 2.15996, 0.0399, 2.17017, 1.10571, 5.45565, 0.9565, 2.60991]}]}}, "hooddoupeng-qian2": {"hooddoupeng-qian": {"deform": [{"time": 0.1667, "offset": 14, "vertices": [-0.98553, -1.7793, -0.28058, -2.01456, -0.45648, -3.62238, 0.87518, -3.54469], "curve": [0.267, 0, 0.467, 1]}, {"time": 0.5667, "offset": 6, "vertices": [-2.66248, 4.10059, -4.60565, 11.58276, -7.10693, 7.49274, -0.25763, 4.54077, 0.80658, -4.65707, 0.51416, -5.31024, -0.45648, -3.62238, 0.87518, -3.54469], "curve": [0.675, 0, 0.892, 1]}, {"time": 1, "offset": 14, "vertices": [-0.98553, -1.7793, -0.28058, -2.01456, -0.45648, -3.62238, 0.87518, -3.54469]}]}}, "hoodmaozi-bei2": {"hoodmaozi-bei": {"deform": [{"time": 0.1667, "offset": 4, "vertices": [-25.30083, 21.6267, -8.04947, 8.42277, 4.79617, -12.42799, -14.02617, 4.76964, -11.79563, 4.30691, -6.25515, -2.40849], "curve": [0.38, 0.29, 0.671, 1]}, {"time": 0.8333, "offset": 4, "vertices": [-10.57351, 12.78316, -2.12094, 4.57733, -7.22677, -11.17029, -8.19859, -14.97982, 5.03276, -24.58457, 3.87043, -17.82349], "curve": [0.881, 0, 0.938, 0.38]}, {"time": 1, "offset": 4, "vertices": [-12.77604, 14.10574, -3.00757, 5.15243, -5.42869, -11.35838, -9.07012, -12.02622, 2.51601, -20.26374, 2.35612, -15.51812]}]}}, "hoodmaozi-zuoqian2": {"hoodmaozi-zuoqian": {"deform": [{"time": 0.1667, "vertices": [1.52595, -6.56361, 1.64924, -6.53015, -0.30488, -8.02085, -0.96301, -7.97477, 0, 0, 0, 0, -7.91293, -8.7874, -8.74125, -7.8237, 0, 0, -5.4869, -5.21065, -5.76621, -5.9799, 2.01803, -0.6716, 4.05224, 4.13115, -2.04547, -13.21218, 4.35057, -11.10321, 4.73514, -11.05116], "curve": [0.348, 0.44, 0.544, 1]}, {"time": 0.6667, "vertices": [2.57062, -11.11018, 2.51404, -11.12318, 0.48877, 10.4646, 1.9718, 10.28878, 0, 0, 0, 0, -7.72711, 0.6479, -7.55664, 1.74014, 0, 0, -5.48621, -4.5835, -6.31104, -5.49704, -0.29163, 5.47552, -1.93097, 0.17159, 0, 0, 9.17194, -11.27444, 9.11407, -11.32089], "curve": [0.753, 0, 0.873, 0.45]}, {"time": 1, "vertices": [1.9627, -8.46444, 2.01079, -8.4504, 0.02693, -0.29247, 0.26397, -0.33916, 0, 0, 0, 0, -7.83524, -4.84269, -8.24599, -3.82526, 0, 0, -5.48661, -4.94846, -5.99399, -5.77803, 1.05241, 1.89839, 1.55078, 2.47574, -1.1903, -7.68844, 6.36629, -11.1748, 6.56588, -11.16393]}]}}, "hoodshenti2": {"hoodshenti": {"deform": [{"time": 0.1667, "vertices": [7.84869, 1.82483, 12.28972, -5.41541, -5.33102, 12.32648, -1.52066, 6.54047, -3.33154, -5.83026, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.50626, -10.64777, -17.12292, -1.61087], "curve": [0.375, 0, 0.792, 1]}, {"time": 1}]}}, "hoodzui2": {"hoodzui": {"deform": [{"time": 0.1667, "vertices": [-0.94337, -5.44817, -2.45786, -4.37368, -3.55056, -2.59521, -3.06083, -1.06238, -1.59148, 3.53642, 2.27599, 5.85695, 6.21954, 11.04055, 1.95906, 4.37871, -0.44398, -1.88076, 2.6095, -8.32726, 2e-05, 1e-05, 1e-05, 3e-05, -0.91664, -1.88792], "curve": [0.375, 0, 0.792, 1]}, {"time": 1}]}}}}}, "hood_win_idle": {"slots": {"bone154": {"attachment": [{}]}, "bone155": {"attachment": [{}]}, "bone156": {"attachment": [{}]}, "bone157": {"attachment": [{}]}, "bone158": {"attachment": [{}]}, "bone159": {"attachment": [{}]}, "bone160": {"attachment": [{}]}, "bone161": {"attachment": [{}]}, "langertou2": {"attachment": [{}]}, "langshenti2": {"attachment": [{}]}, "langshetou2": {"attachment": [{}]}, "langshizhi2": {"attachment": [{}]}, "langshou-bei2": {"attachment": [{}]}, "langshou-qian2": {"attachment": [{}]}, "langshou-you2": {"attachment": [{}]}, "langshouzhi-qian2": {"attachment": [{}]}, "langweiba2": {"attachment": [{}]}, "langxiaer2": {"attachment": [{}]}, "texiao-guang2": {"rgba": [{"color": "ffffffc7", "curve": [0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 0.9, 0.247, 1]}, {"time": 0.3333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff66", "curve": [1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 0.4, 1.485, 0.61]}, {"time": 1.6667, "color": "ffffffc7"}]}, "texiaoixiantiaoguang2": {"rgba": [{"color": "ffffff5d", "curve": [0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 0.16, 0.247, 0]}, {"time": 0.3333, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": [1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 1, 1.485, 0.64]}, {"time": 1.6667, "color": "ffffff5d"}]}, "texiaoixiantiaoguang3": {"rgba": [{"color": "ffffffdd", "curve": [0.213, 1, 0.505, 1, 0.213, 1, 0.505, 1, 0.213, 1, 0.505, 1, 0.213, 0.62, 0.505, 0]}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": [1.547, 1, 1.604, 1, 1.547, 1, 1.604, 1, 1.547, 1, 1.604, 1, 1.547, 1, 1.604, 0.95]}, {"time": 1.6667, "color": "ffffffdd"}]}}, "bones": {"bone16": {"rotate": [{"value": -2.96, "curve": [0.102, -3.7, 0.195, -4.23]}, {"time": 0.2667, "value": -4.23, "curve": [0.467, -4.23, 0.867, 0.83]}, {"time": 1.0667, "value": 0.83, "curve": [1.212, 0.83, 1.459, -1.52]}, {"time": 1.6667, "value": -2.96}]}, "bone2": {"translate": [{"curve": [0.208, 0, 0.625, 0, 0.208, 0, 0.625, 29.95]}, {"time": 0.8333, "y": 29.95, "curve": [1.042, 0, 1.458, 0, 1.042, 29.95, 1.458, 0]}, {"time": 1.6667}]}, "bone3": {"rotate": [{"value": 0.37, "curve": [0.076, 0.15, 0.145, 0]}, {"time": 0.2, "curve": [0.408, 0, 0.825, 2.12]}, {"time": 1.0333, "value": 2.12, "curve": [1.187, 2.12, 1.457, 0.93]}, {"time": 1.6667, "value": 0.37}], "translate": [{"curve": [0.208, 0, 0.625, 2.47, 0.208, 0, 0.625, 10.6]}, {"time": 0.8333, "x": 2.47, "y": 10.6, "curve": [1.042, 2.47, 1.458, 0, 1.042, 10.6, 1.458, 0]}, {"time": 1.6667}]}, "bone4": {"rotate": [{"value": -1.11, "curve": [0.126, -1.9, 0.249, -2.63]}, {"time": 0.3333, "value": -2.63, "curve": [0.567, -2.63, 1.033, 0.82]}, {"time": 1.2667, "value": 0.82, "curve": [1.365, 0.82, 1.519, -0.18]}, {"time": 1.6667, "value": -1.11}]}, "bone5": {"rotate": [{"value": -3.16, "curve": [0.102, -4.73, 0.196, -5.91]}, {"time": 0.2667, "value": -5.91, "curve": [0.483, -5.91, 0.917, 3.8]}, {"time": 1.1333, "value": 3.8, "curve": [1.263, 3.8, 1.479, -0.38]}, {"time": 1.6667, "value": -3.16}]}, "bone10": {"rotate": [{"value": -2.56, "curve": [0.2, 0.44, 0.455, 6.52]}, {"time": 0.6, "value": 6.52, "curve": [0.817, 6.52, 1.25, -4.62]}, {"time": 1.4667, "value": -4.62, "curve": [1.522, -4.62, 1.591, -3.8]}, {"time": 1.6667, "value": -2.56}]}, "bone15": {"rotate": [{"curve": [0.208, 0, 0.625, 7.04]}, {"time": 0.8333, "value": 7.04, "curve": [1.042, 7.04, 1.458, 0]}, {"time": 1.6667}]}, "bone17": {"rotate": [{"curve": [0.208, 0, 0.625, -6.78]}, {"time": 0.8333, "value": -6.78, "curve": [1.042, -6.78, 1.458, 0]}, {"time": 1.6667}]}, "bone18": {"rotate": [{"value": 1.64, "curve": [0.151, 3.25, 0.299, 4.73]}, {"time": 0.4, "value": 4.73, "curve": [0.6, 4.73, 1, -2.15]}, {"time": 1.2, "value": -2.15, "curve": [1.316, -2.15, 1.494, -0.18]}, {"time": 1.6667, "value": 1.64}]}, "bone19": {"rotate": [{"value": -1.59, "curve": [0.208, -1.59, 0.625, 9.12]}, {"time": 0.8333, "value": 9.12, "curve": [1.042, 9.12, 1.458, -1.59]}, {"time": 1.6667, "value": -1.59}]}, "bone20": {"translate": [{"curve": [0.208, 0, 0.625, 6.49, 0.208, 0, 0.625, 1.51]}, {"time": 0.8333, "x": 6.49, "y": 1.51, "curve": [1.042, 6.49, 1.458, 0, 1.042, 1.51, 1.458, 0]}, {"time": 1.6667}]}, "bone21": {"rotate": [{"value": -1.75, "curve": [0.213, 0.15, 0.505, 4.79]}, {"time": 0.6667, "value": 4.79, "curve": [0.875, 4.79, 1.292, -2.72]}, {"time": 1.5, "value": -2.72, "curve": [1.547, -2.72, 1.604, -2.35]}, {"time": 1.6667, "value": -1.75}]}, "bone23": {"rotate": [{"value": -5.97, "curve": [0.049, -6.42, 0.094, -6.69]}, {"time": 0.1333, "value": -6.69, "curve": [0.333, -6.69, 0.733, 1.71]}, {"time": 0.9333, "value": 1.71, "curve": [1.112, 1.71, 1.442, -4.13]}, {"time": 1.6667, "value": -5.97}]}, "bone25": {"rotate": [{"value": 0.09, "curve": [0.151, -4.23, 0.299, -8.22]}, {"time": 0.4, "value": -8.22, "curve": [0.6, -8.22, 1, 10.27]}, {"time": 1.2, "value": 10.27, "curve": [1.316, 10.27, 1.494, 4.98]}, {"time": 1.6667, "value": 0.09}]}, "bone26": {"rotate": [{"value": -1.29, "curve": [0.213, -6.28, 0.505, -18.5]}, {"time": 0.6667, "value": -18.5, "curve": [0.875, -18.5, 1.292, 1.29]}, {"time": 1.5, "value": 1.29, "curve": [1.547, 1.29, 1.604, 0.31]}, {"time": 1.6667, "value": -1.29}]}, "bone28": {"rotate": [{"value": 5.5, "curve": [0.063, 6.44, 0.119, 7.02]}, {"time": 0.1667, "value": 7.02, "curve": [0.375, 7.02, 0.792, -4.67]}, {"time": 1, "value": -4.67, "curve": [1.162, -4.67, 1.453, 2.55]}, {"time": 1.6667, "value": 5.5}]}, "bone30": {"rotate": [{"value": 3.11, "curve": [0.152, 9.19, 0.298, 14.58]}, {"time": 0.4, "value": 14.58, "curve": [0.583, 14.58, 0.95, -13.71]}, {"time": 1.1333, "value": -13.71, "curve": [1.265, -13.71, 1.471, -4.63]}, {"time": 1.6667, "value": 3.11}]}, "bone31": {"rotate": [{"value": 0.97, "curve": [0.076, 1.72, 0.145, 2.22]}, {"time": 0.2, "value": 2.22, "curve": [0.417, 2.22, 0.85, -4.55]}, {"time": 1.0667, "value": -4.55, "curve": [1.212, -4.55, 1.467, -0.85]}, {"time": 1.6667, "value": 0.97}], "translate": [{"x": -0.49, "y": -8.78, "curve": [0.076, 0.33, 0.145, 0.88, 0.076, -8.35, 0.145, -8.06]}, {"time": 0.2, "x": 0.88, "y": -8.06, "curve": [0.417, 0.88, 0.85, -6.55, 0.417, -8.06, 0.85, -11.94]}, {"time": 1.0667, "x": -6.55, "y": -11.94, "curve": [1.212, -6.55, 1.467, -2.49, 1.212, -11.94, 1.467, -9.82]}, {"time": 1.6667, "x": -0.49, "y": -8.78}]}, "bone32": {"rotate": [{"curve": [0.208, 0, 0.625, -3.75]}, {"time": 0.8333, "value": -3.75, "curve": [1.042, -3.75, 1.458, 0]}, {"time": 1.6667}]}, "bone34": {"rotate": [{"value": 0.88, "curve": [0.125, 3.86, 0.25, 6.83]}, {"time": 0.3333, "value": 6.83, "curve": [0.583, 6.83, 1.083, -5.06]}, {"time": 1.3333, "value": -5.06, "curve": [1.417, -5.06, 1.542, -2.09]}, {"time": 1.6667, "value": 0.88}]}, "bone36": {"rotate": [{"value": 1.81, "curve": [0.185, 4.54, 0.377, 7.62]}, {"time": 0.5, "value": 7.62, "curve": [0.692, 7.62, 1.075, -2.5]}, {"time": 1.2667, "value": -2.5, "curve": [1.368, -2.5, 1.515, -0.47]}, {"time": 1.6667, "value": 1.81}]}, "bone38": {"rotate": [{"value": -7.07, "curve": [0.063, -7.93, 0.119, -8.45]}, {"time": 0.1667, "value": -8.45, "curve": [0.375, -8.45, 0.792, 2.22]}, {"time": 1, "value": 2.22, "curve": [1.162, 2.22, 1.453, -4.38]}, {"time": 1.6667, "value": -7.07}], "translate": [{"x": -3.39, "y": -4.32, "curve": [0.063, -4.12, 0.119, -4.57, 0.063, -4.56, 0.119, -4.7]}, {"time": 0.1667, "x": -4.57, "y": -4.7, "curve": [0.375, -4.57, 0.792, 4.52, 0.375, -4.7, 0.792, -1.77]}, {"time": 1, "x": 4.52, "y": -1.77, "curve": [1.162, 4.52, 1.453, -1.1, 1.162, -1.77, 1.453, -3.58]}, {"time": 1.6667, "x": -3.39, "y": -4.32}]}, "bone41": {"rotate": [{"value": -3, "curve": [0.064, -5.5, 0.123, -7.38]}, {"time": 0.1667, "value": -7.38, "curve": [0.458, -7.38, 1.042, 8.07]}, {"time": 1.3333, "value": 8.07, "curve": [1.414, 8.07, 1.55, 1.43]}, {"time": 1.6667, "value": -3}]}, "bone43": {"rotate": [{"curve": [0.208, 0, 0.625, -21.04]}, {"time": 0.8333, "value": -21.04, "curve": [1.042, -21.04, 1.458, 0]}, {"time": 1.6667}]}, "bone44": {"rotate": [{"value": -2.84, "curve": [0.021, -4.03, 0.1, -8.52]}, {"time": 0.1333, "value": -8.52, "curve": [0.233, -8.52, 0.433, 0.66]}, {"time": 0.5333, "value": 0.66, "curve": [0.642, 0.66, 0.858, -18.26]}, {"time": 0.9667, "value": -18.26, "curve": [1.067, -18.26, 1.267, -3.15]}, {"time": 1.3667, "value": -3.15, "curve": [1.442, -3.15, 1.619, -2.9]}, {"time": 1.6667, "value": -2.84}], "scale": [{"curve": [0.027, 1.013, 0.125, 1.063, 0.027, 1, 0.125, 1]}, {"time": 0.1667, "x": 1.063, "curve": [0.267, 1.063, 0.467, 0.814, 0.267, 1, 0.467, 1]}, {"time": 0.5667, "x": 0.814, "curve": [0.683, 0.814, 0.917, 1.01, 0.683, 1, 0.917, 1]}, {"time": 1.0333, "x": 1.01, "curve": [1.125, 1.01, 1.308, 0.85, 1.125, 1, 1.308, 1]}, {"time": 1.4, "x": 0.85, "curve": [1.467, 0.85, 1.624, 0.97, 1.467, 1, 1.624, 1]}, {"time": 1.6667}]}, "bone51": {"rotate": [{"value": 3.45, "curve": [0.102, 5.27, 0.196, 6.65]}, {"time": 0.2667, "value": 6.65, "curve": [0.483, 6.65, 0.917, -4.62]}, {"time": 1.1333, "value": -4.62, "curve": [1.263, -4.62, 1.479, 0.22]}, {"time": 1.6667, "value": 3.45}]}, "bone53": {"rotate": [{"value": -1.26, "curve": [0.177, 2.77, 0.378, 8.56]}, {"time": 0.5, "value": 8.56, "curve": [0.725, 8.56, 1.175, -5.5]}, {"time": 1.4, "value": -5.5, "curve": [1.47, -5.5, 1.565, -3.68]}, {"time": 1.6667, "value": -1.26}]}, "bone54": {"rotate": [{"value": -0.89, "curve": [0.127, -2.35, 0.248, -3.59]}, {"time": 0.3333, "value": -3.59, "curve": [0.55, -3.59, 0.983, 3.33]}, {"time": 1.2, "value": 3.33, "curve": [1.315, 3.33, 1.496, 1.01]}, {"time": 1.6667, "value": -0.89}]}, "bone56": {"rotate": [{"value": 4.85, "curve": [0.2, 0.86, 0.455, -7.24]}, {"time": 0.6, "value": -7.24, "curve": [0.817, -7.24, 1.25, 7.58]}, {"time": 1.4667, "value": 7.58, "curve": [1.522, 7.58, 1.591, 6.49]}, {"time": 1.6667, "value": 4.85}]}, "bone57": {"rotate": [{"curve": [0.208, 0, 0.625, 11.02]}, {"time": 0.8333, "value": 11.02, "curve": [1.042, 11.02, 1.458, 0]}, {"time": 1.6667}]}, "bone59": {"rotate": [{"value": -2.33, "curve": [0.126, -5.64, 0.249, -8.69]}, {"time": 0.3333, "value": -8.69, "curve": [0.567, -8.69, 1.033, 5.76]}, {"time": 1.2667, "value": 5.76, "curve": [1.365, 5.76, 1.519, 1.55]}, {"time": 1.6667, "value": -2.33}]}, "bone61": {"rotate": [{"value": 6.22, "curve": [0.208, -0.24, 0.454, -10.78]}, {"time": 0.6, "value": -10.78, "curve": [0.8, -10.78, 1.2, 11.95]}, {"time": 1.4, "value": 11.95, "curve": [1.471, 11.95, 1.565, 9.54]}, {"time": 1.6667, "value": 6.22}]}, "bone62": {"rotate": [{"value": -2.36, "curve": [0.076, -3, 0.145, -3.43]}, {"time": 0.2, "value": -3.43, "curve": [0.417, -3.43, 0.85, 2.38]}, {"time": 1.0667, "value": 2.38, "curve": [1.212, 2.38, 1.467, -0.8]}, {"time": 1.6667, "value": -2.36}]}, "bone64": {"rotate": [{"value": 1.21, "curve": [0.182, 1.08, 0.378, 0.91]}, {"time": 0.5, "value": 0.91, "curve": [0.708, 0.91, 1.125, 4.19]}, {"time": 1.3333, "value": 4.19, "curve": [1.419, 4.19, 1.54, 2.85]}, {"time": 1.6667, "value": 1.21}]}, "bone66": {"rotate": [{"curve": [0.208, 0, 0.625, -7.01]}, {"time": 0.8333, "value": -7.01, "curve": [1.042, -7.01, 1.458, 0]}, {"time": 1.6667}]}, "bone68": {"rotate": [{"curve": [0.208, 0, 0.625, -5.33]}, {"time": 0.8333, "value": -5.33, "curve": [1.042, -5.33, 1.458, 0]}, {"time": 1.6667}]}, "bone70": {"rotate": [{"value": 1.73, "curve": [0.102, 3.21, 0.196, 4.33]}, {"time": 0.2667, "value": 4.33, "curve": [0.483, 4.33, 0.917, -4.81]}, {"time": 1.1333, "value": -4.81, "curve": [1.263, -4.81, 1.479, -0.89]}, {"time": 1.6667, "value": 1.73}]}, "bone72": {"rotate": [{"value": -7.35, "curve": [0.049, -8.22, 0.094, -8.73]}, {"time": 0.1333, "value": -8.73, "curve": [0.333, -8.73, 0.733, 7.35]}, {"time": 0.9333, "value": 7.35, "curve": [1.112, 7.35, 1.442, -3.82]}, {"time": 1.6667, "value": -7.35}]}, "bone73": {"rotate": [{"value": 1.31}]}, "bone75": {"rotate": [{"curve": [0.208, 0, 0.625, 3.94]}, {"time": 0.8333, "value": 3.94, "curve": [1.042, 3.94, 1.458, 0]}, {"time": 1.6667}]}, "bone77": {"rotate": [{"value": -0.8, "curve": [0.115, -4.33, 0.221, -7.1]}, {"time": 0.3, "value": -7.1, "curve": [0.508, -7.1, 0.925, 12.75]}, {"time": 1.1333, "value": 12.75, "curve": [1.263, 12.75, 1.477, 4.89]}, {"time": 1.6667, "value": -0.8}]}, "bone79": {"rotate": [{"curve": [0.208, 0, 0.625, 12.95]}, {"time": 0.8333, "value": 12.95, "curve": [1.042, 12.95, 1.458, 0]}, {"time": 1.6667}]}, "bone83": {"rotate": [{"value": 1.83, "curve": [0.075, 0.29, 0.375, -5.49]}, {"time": 0.5, "value": -5.49, "curve": [0.708, -5.49, 1.125, 6.09]}, {"time": 1.3333, "value": 6.09, "curve": [1.417, 6.09, 1.615, 2.64]}, {"time": 1.6667, "value": 1.83}]}, "bone87": {"rotate": [{"curve": [0.1, 0, 0.3, -28.29]}, {"time": 0.4, "value": -28.29, "curve": [0.508, -28.29, 0.725, 0]}, {"time": 0.8333, "curve": [0.933, 0, 1.133, -28.29]}, {"time": 1.2333, "value": -28.29, "curve": [1.342, -28.29, 1.558, 0]}, {"time": 1.6667}]}, "bone89": {"rotate": [{"value": 1.48, "curve": [0.026, 4.3, 0.125, 16.3]}, {"time": 0.1667, "value": 16.3, "curve": [0.258, 16.3, 0.442, 2.55]}, {"time": 0.5333, "value": 2.55, "curve": [0.625, 2.55, 0.808, 13.22]}, {"time": 0.9, "value": 13.22, "curve": [1.008, 13.22, 1.225, -5.77]}, {"time": 1.3333, "value": -5.77, "curve": [1.417, -5.77, 1.616, 0.1]}, {"time": 1.6667, "value": 1.48}]}, "bone92": {"rotate": [{"curve": [0.1, 0, 0.3, 16.11]}, {"time": 0.4, "value": 16.11, "curve": [0.508, 16.11, 0.725, 0]}, {"time": 0.8333, "curve": [0.933, 0, 1.133, 16.11]}, {"time": 1.2333, "value": 16.11, "curve": [1.342, 16.11, 1.558, 0]}, {"time": 1.6667}]}, "bone99": {"rotate": [{"curve": [0.208, 0, 0.625, -3.81]}, {"time": 0.8333, "value": -3.81, "curve": [1.042, -3.81, 1.458, 0]}, {"time": 1.6667}]}, "bone100": {"rotate": [{"value": 3.32, "curve": [0.05, 3.72, 0.095, 3.96]}, {"time": 0.1333, "value": 3.96, "curve": [0.367, 3.96, 0.833, -1.85]}, {"time": 1.0667, "value": -1.85, "curve": [1.212, -1.85, 1.478, 1.92]}, {"time": 1.6667, "value": 3.32}]}, "bone102": {"rotate": [{"value": -0.1, "curve": [0.159, 2.42, 0.326, 5.26]}, {"time": 0.4333, "value": 5.26, "curve": [0.658, 5.26, 1.108, -3.88]}, {"time": 1.3333, "value": -3.88, "curve": [1.418, -3.88, 1.54, -2.1]}, {"time": 1.6667, "value": -0.1}]}, "bone104": {"rotate": [{"curve": [0.208, 0, 0.625, 7.25]}, {"time": 0.8333, "value": 7.25, "curve": [1.042, 7.25, 1.458, 0]}, {"time": 1.6667}]}, "bone95": {"rotate": [{"value": -0.11, "curve": [0.182, -1.28, 0.378, -2.76]}, {"time": 0.5, "value": -2.76, "curve": [0.708, -2.76, 1.125, 1.43]}, {"time": 1.3333, "value": 1.43, "curve": [1.419, 1.43, 1.54, 0.74]}, {"time": 1.6667, "value": -0.11}]}, "bone97": {"rotate": [{"curve": [0.208, 0, 0.625, -3.17]}, {"time": 0.8333, "value": -3.17, "curve": [1.042, -3.17, 1.458, 0]}, {"time": 1.6667}]}, "bone105": {"rotate": [{"value": 5.8, "curve": [0.102, 8.13, 0.195, 9.81]}, {"time": 0.2667, "value": 9.81, "curve": [0.467, 9.81, 0.867, -6.13]}, {"time": 1.0667, "value": -6.13, "curve": [1.212, -6.13, 1.459, 1.27]}, {"time": 1.6667, "value": 5.8}]}, "bone106": {"rotate": [{}, {"time": 0.0333, "value": 0.01, "curve": "stepped"}, {"time": 0.1, "value": 0.01}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2}, {"time": 0.2333, "value": 0.01}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.3667, "value": 0.01}, {"time": 0.4}, {"time": 0.4333, "value": 0.01}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.7, "value": 0.01, "curve": "stepped"}, {"time": 0.8, "value": 0.01}, {"time": 0.8333}, {"time": 0.8667, "value": 0.01}, {"time": 1}, {"time": 1.0333, "value": 0.01}, {"time": 1.6667}], "scale": [{"curve": [0.142, 1, 0.425, 0.875, 0.142, 1, 0.425, 1.016]}, {"time": 0.5667, "x": 0.875, "y": 1.016, "curve": [0.742, 0.875, 1.092, 1.02, 0.742, 1.016, 1.092, 1.03]}, {"time": 1.2667, "x": 1.02, "y": 1.03, "curve": [1.367, 1.02, 1.567, 1, 1.367, 1.03, 1.567, 1]}, {"time": 1.6667}]}, "bone107": {"rotate": [{"value": -0.36, "curve": [0.182, 1.59, 0.378, 4.07]}, {"time": 0.5, "value": 4.07, "curve": [0.708, 4.07, 1.125, -2.94]}, {"time": 1.3333, "value": -2.94, "curve": [1.419, -2.94, 1.54, -1.78]}, {"time": 1.6667, "value": -0.36}]}, "bone109": {"rotate": [{"value": 4.07, "curve": [0.208, 4.07, 0.625, 9.22]}, {"time": 0.8333, "value": 9.22, "curve": [1.042, 9.22, 1.458, 4.07]}, {"time": 1.6667, "value": 4.07}], "shear": [{"curve": [0.217, 0, 0.65, 2.45, 0.217, 0, 0.65, 1.32]}, {"time": 0.8667, "x": 2.45, "y": 1.32, "curve": [1.067, 2.45, 1.467, 0, 1.067, 1.32, 1.467, 0]}, {"time": 1.6667}]}, "bone111": {"rotate": [{"value": -15.99, "curve": [0.102, -16.94, 0.195, -17.62]}, {"time": 0.2667, "value": -17.62, "curve": [0.467, -17.62, 0.867, -11.12]}, {"time": 1.0667, "value": -11.12, "curve": [1.212, -11.12, 1.459, -14.14]}, {"time": 1.6667, "value": -15.99}]}, "bone121": {"rotate": [{"value": -1.7, "curve": [0.151, -3.63, 0.299, -5.42]}, {"time": 0.4, "value": -5.42, "curve": [0.6, -5.42, 1, 2.86]}, {"time": 1.2, "value": 2.86, "curve": [1.316, 2.86, 1.494, 0.49]}, {"time": 1.6667, "value": -1.7}]}, "bone123": {"rotate": [{"curve": [0.208, 0, 0.625, -11.03]}, {"time": 0.8333, "value": -11.03, "curve": [1.042, -11.03, 1.458, 0]}, {"time": 1.6667}]}, "bone124": {"rotate": [{"curve": [0.208, 0, 0.625, -6.85]}, {"time": 0.8333, "value": -6.85, "curve": [1.042, -6.85, 1.458, 0]}, {"time": 1.6667}]}, "bone126": {"rotate": [{"value": -4.46, "curve": [0.102, -7.85, 0.198, -10.62]}, {"time": 0.2667, "value": -10.62, "curve": [0.517, -10.62, 1.017, 6.13]}, {"time": 1.2667, "value": 6.13, "curve": [1.365, 6.13, 1.521, 0.2]}, {"time": 1.6667, "value": -4.46}]}, "bone127": {"rotate": [{"value": 1.42, "curve": [0.179, -4.05, 0.378, -11.3]}, {"time": 0.5, "value": -11.3, "curve": [0.717, -11.3, 1.15, 7.84]}, {"time": 1.3667, "value": 7.84, "curve": [1.445, 7.84, 1.552, 5.02]}, {"time": 1.6667, "value": 1.42}]}, "bone146": {"rotate": [{"curve": [0.208, 0, 0.625, 14.62]}, {"time": 0.8333, "value": 14.62, "curve": [1.042, 14.62, 1.458, 0]}, {"time": 1.6667}], "translate": [{"curve": [0.208, 0, 0.625, 4.4, 0.208, 0, 0.625, 5.05]}, {"time": 0.8333, "x": 4.4, "y": 5.05, "curve": [1.042, 4.4, 1.458, 0, 1.042, 5.05, 1.458, 0]}, {"time": 1.6667}]}, "bone147": {"rotate": [{"curve": [0.208, 0, 0.625, -17.57]}, {"time": 0.8333, "value": -17.57, "curve": [1.042, -17.57, 1.458, 0]}, {"time": 1.6667}]}, "bone148": {"rotate": [{"value": 6.11, "curve": [0.177, -1.7, 0.378, -12.94]}, {"time": 0.5, "value": -12.94, "curve": [0.725, -12.94, 1.175, 14.36]}, {"time": 1.4, "value": 14.36, "curve": [1.47, 14.36, 1.565, 10.81]}, {"time": 1.6667, "value": 6.11}], "translate": [{"x": 0.99, "y": -0.93, "curve": [0.177, 1.93, 0.378, 3.29, 0.177, 0.22, 0.378, 1.87]}, {"time": 0.5, "x": 3.29, "y": 1.87, "curve": [0.725, 3.29, 1.175, 0, 0.725, 1.87, 1.175, -2.14]}, {"time": 1.4, "y": -2.14, "curve": [1.47, 0, 1.565, 0.43, 1.47, -2.14, 1.565, -1.62]}, {"time": 1.6667, "x": 0.99, "y": -0.93}]}, "bone149": {"rotate": [{"value": 7.16, "curve": [0.076, 9.65, 0.145, 11.31]}, {"time": 0.2, "value": 11.31, "curve": [0.417, 11.31, 0.85, -11.19]}, {"time": 1.0667, "value": -11.19, "curve": [1.212, -11.19, 1.467, 1.1]}, {"time": 1.6667, "value": 7.16}]}, "bone136": {"rotate": [{"value": 0.32, "curve": [0.15, -1.29, 0.3, -2.9]}, {"time": 0.4, "value": -2.9, "curve": [0.617, -2.9, 1.05, 3.55]}, {"time": 1.2667, "value": 3.55, "curve": [1.367, 3.55, 1.517, 1.93]}, {"time": 1.6667, "value": 0.32}]}, "bone138": {"rotate": [{"curve": [0.208, 0, 0.625, -4.75]}, {"time": 0.8333, "value": -4.75, "curve": [1.042, -4.75, 1.458, 0]}, {"time": 1.6667}]}, "bone140": {"rotate": [{"value": 7.08, "curve": [0.076, 10.33, 0.147, 12.78]}, {"time": 0.2, "value": 12.78, "curve": [0.467, 12.78, 1, -7.3]}, {"time": 1.2667, "value": -7.3, "curve": [1.364, -7.3, 1.526, 1.33]}, {"time": 1.6667, "value": 7.08}]}, "bone141": {"rotate": [{"curve": [0.208, 0, 0.625, 4.2]}, {"time": 0.8333, "value": 4.2, "curve": [1.042, 4.2, 1.458, 0]}, {"time": 1.6667}]}, "bone143": {"rotate": [{"value": -1.73, "curve": [0.102, -3.01, 0.197, -4.01]}, {"time": 0.2667, "value": -4.01, "curve": [0.5, -4.01, 0.967, 3.09]}, {"time": 1.2, "value": 3.09, "curve": [1.314, 3.09, 1.5, 0.29]}, {"time": 1.6667, "value": -1.73}]}, "bone145": {"rotate": [{"value": 5.41, "curve": [0.208, 0.72, 0.454, -6.93]}, {"time": 0.6, "value": -6.93, "curve": [0.8, -6.93, 1.2, 9.56]}, {"time": 1.4, "value": 9.56, "curve": [1.471, 9.56, 1.565, 7.82]}, {"time": 1.6667, "value": 5.41}]}, "bone150": {"translate": [{"x": 1754.76}]}, "bone221": {"translate": [{"x": 18.75, "y": -0.4}]}, "bone131": {"rotate": [{"curve": [0.208, 0, 0.625, -17.19]}, {"time": 0.8333, "value": -17.19, "curve": [1.042, -17.19, 1.458, 0]}, {"time": 1.6667}], "translate": [{"curve": [0.208, 0, 0.625, 0, 0.208, 0, 0.625, -10]}, {"time": 0.8333, "y": -10, "curve": [1.042, 0, 1.458, 0, 1.042, -10, 1.458, 0]}, {"time": 1.6667}]}, "bone46": {"rotate": [{"value": 5.26, "curve": [0.031, 2.64, 0.15, -8.51]}, {"time": 0.2, "value": -8.51, "curve": [0.3, -8.51, 0.5, 20.88]}, {"time": 0.6, "value": 20.88, "curve": [0.708, 20.88, 0.925, -10.57]}, {"time": 1.0333, "value": -10.57, "curve": [1.133, -10.57, 1.333, 20.88]}, {"time": 1.4333, "value": 20.88, "curve": [1.492, 20.88, 1.631, 8.54]}, {"time": 1.6667, "value": 5.26}], "scale": [{"curve": [0.032, 1.013, 0.15, 1.063, 0.032, 1, 0.15, 1]}, {"time": 0.2, "x": 1.063, "curve": [0.3, 1.063, 0.5, 0.955, 0.3, 1, 0.5, 1]}, {"time": 0.6, "x": 0.955, "curve": [0.717, 0.955, 0.95, 1.063, 0.717, 1, 0.95, 1]}, {"time": 1.0667, "x": 1.063, "curve": [1.158, 1.063, 1.342, 0.955, 1.158, 1, 1.342, 1]}, {"time": 1.4333, "x": 0.955, "curve": [1.492, 0.955, 1.629, 0.991, 1.492, 1, 1.629, 1]}, {"time": 1.6667}]}, "bone48": {"rotate": [{"value": 5.26, "curve": [0.048, -0.22, 0.225, -20.82]}, {"time": 0.3, "value": -20.82, "curve": [0.4, -20.82, 0.6, 20.88]}, {"time": 0.7, "value": 20.88, "curve": [0.808, 20.88, 1.025, -17.5]}, {"time": 1.1333, "value": -17.5, "curve": [1.233, -17.5, 1.433, 20.88]}, {"time": 1.5333, "value": 20.88, "curve": [1.567, 20.88, 1.645, 8.38]}, {"time": 1.6667, "value": 5.26}], "scale": [{"curve": [0.048, 1.013, 0.225, 1.063, 0.048, 1, 0.225, 1]}, {"time": 0.3, "x": 1.063, "curve": [0.4, 1.063, 0.6, 0.955, 0.4, 1, 0.6, 1]}, {"time": 0.7, "x": 0.955, "curve": [0.817, 0.955, 1.05, 1.063, 0.817, 1, 1.05, 1]}, {"time": 1.1667, "x": 1.063, "curve": [1.258, 1.063, 1.442, 0.955, 1.258, 1, 1.442, 1]}, {"time": 1.5333, "x": 0.955, "curve": [1.567, 0.955, 1.645, 0.991, 1.567, 1, 1.645, 1]}, {"time": 1.6667}]}}, "attachments": {"default": {"bone8": {"hoodlian": {"deform": [{"time": 0.0667, "vertices": [0.06599, -0.06271, -0.05833, -0.0701, -0.06601, 0.06282, 0.05835, 0.0701, 0.01402, -0.01555]}, {"time": 0.2667, "vertices": [0.04409, -0.0419, -0.03894, -0.04691, -0.04416, 0.04201, 0.039, 0.04691, 0.00932, -0.01055]}, {"time": 0.5, "vertices": [0.0594, -0.05661, -0.05244, -0.06314, -0.05951, 0.05659, 0.05249, 0.06302, 0.01259, -0.01409]}, {"time": 0.7, "vertices": [0.09393, -0.08932, -0.0829, -0.09958, -0.0939, 0.08955, 0.08304, 0.0997, 0.01997, -0.02227]}, {"time": 1.0333, "vertices": [0.07815, -0.07437, -0.06903, -0.08292, -0.07822, 0.07454, 0.06909, 0.08298, 0.01659, -0.01855]}, {"time": 1.1333, "vertices": [0.09304, -0.08859, -0.0822, -0.09866, -0.09308, 0.08876, 0.08224, 0.09879, 0.01974, -0.02202]}, {"time": 1.1667, "vertices": [0.12432, -0.11838, -0.10984, -0.13187, -0.12433, 0.11855, 0.10992, 0.13199, 0.0264, -0.02935]}, {"time": 1.3333, "vertices": [0.12548, -0.11954, -0.11089, -0.13315, -0.12552, 0.11971, 0.11093, 0.13327, 0.02663, -0.02965]}, {"time": 1.6333, "vertices": [0.14176, -0.13498, -0.1253, -0.15036, -0.14185, 0.13527, 0.12534, 0.1506, 0.03008, -0.0335]}]}}, "bone9": {"hoodlian-ying": {"deform": [{"offset": 2, "vertices": [-13.07286, -25.77524]}, {"time": 0.4333, "offset": 2, "vertices": [12.37006, -24.14557, 0, 2e-05, 3e-05, 5e-05, 0.82425, -6.67245], "curve": [0.592, 0, 0.908, 1]}, {"time": 1.0667, "offset": 2, "vertices": [-24.87318, 40.24612, 0, 2e-05, 3e-05, 5e-05, -12.6295, 33.12016], "curve": [1.217, 0, 1.517, 1]}, {"time": 1.6667, "offset": 2, "vertices": [-13.07286, -25.77524]}]}}, "bone32": {"hoodlanzibu": {"deform": [{"vertices": [-1.82899, -0.17451, -6.62571, -7.71366, -0.87929, -3.38304, 1.87279, -2.67202, 2.88285, 1.52853, 2.19304, 2.41611, -2.91226, -3.26755, -1.21402, -3.83659, -1.24361, 1.05683, -0.80703, 1.21658, -0.98668, 1.50434, 0.88401, 3.24588, 4.77047, -2.04325, 1.0855, 5.07791, -1.27558, 1.38702, -0.23193, 1.5041, 0.43747, 0.85711, -0.83459, 0.4792, 0, 0, 0.21397, 2.15996, 0.0399, 2.17017, 1.10571, 5.45565, 0.9565, 2.60991], "curve": [0.074, 0.49, 0.15, 1]}, {"time": 0.2, "vertices": [-3.61389, -4.49602, -7.39549, -8.19777, -1.37672, -5.26842, 0, 0, 0, 0, 0, 0, 1.1362, -10.15591, 5.22634, -8.78168, -2.22514, -3.7627, -2.8871, -3.28232, -3.41377, -6.73373, 0.1689, 2.36096, 8.39929, 5.1246, -7.11786, 6.55073, -1.19427, -5.05881, -3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.12888, 1.42101], "curve": [0.417, 0, 0.85, 1]}, {"time": 1.0667, "vertices": [-0.27623, 3.58497, -5.95604, -7.29252, -0.44654, -1.74286, 3.50201, -4.99654, 5.39078, 2.85828, 4.10086, 4.51801, -6.43421, 2.72496, -6.8168, 0.46538, -0.38973, 5.24955, 1.00252, 5.13037, 1.12475, 8.67104, 1.50613, 4.01571, -0.8919, -23.53208, 22.98864, -1.94551, -1.34632, 6.99455, -0.43366, 2.81259, 0.81804, 1.60275, -1.56064, 0.89607, 0, 0, 0.40012, 4.039, 0.07462, 4.0581, 3.04968, 8.96557, 1.7886, 4.8804], "curve": [1.218, 0, 1.44, 0.49]}, {"time": 1.6667, "vertices": [-1.82899, -0.17451, -6.62571, -7.71366, -0.87929, -3.38304, 1.87279, -2.67202, 2.88285, 1.52853, 2.19304, 2.41611, -2.91226, -3.26755, -1.21402, -3.83659, -1.24361, 1.05683, -0.80703, 1.21658, -0.98668, 1.50434, 0.88401, 3.24588, 4.77047, -2.04325, 1.0855, 5.07791, -1.27558, 1.38702, -0.23193, 1.5041, 0.43747, 0.85711, -0.83459, 0.4792, 0, 0, 0.21397, 2.15996, 0.0399, 2.17017, 1.10571, 5.45565, 0.9565, 2.60991]}]}}, "hooddoupeng-bei2": {"hooddoupeng-bei": {"deform": [{"curve": [0.1, 0, 0.3, 1]}, {"time": 0.4, "offset": 2, "vertices": [-9.92358, -45.16364, -40.87143, -21.62823], "curve": [0.508, 0, 0.725, 1]}, {"time": 0.8333, "curve": [0.933, 0, 1.133, 1]}, {"time": 1.2333, "offset": 2, "vertices": [-21.75006, -47.89392, -50.6006, -14.37036], "curve": [1.342, 0, 1.558, 1]}, {"time": 1.6667}]}}, "hooddoupeng-qian2": {"hooddoupeng-qian": {"deform": [{"offset": 14, "vertices": [-0.98553, -1.7793, -0.28058, -2.01456, -0.45648, -3.62238, 0.87518, -3.54469], "curve": [0.1, 0, 0.3, 1]}, {"time": 0.4, "offset": 6, "vertices": [-2.66248, 4.10059, -4.60565, 11.58276, -7.10693, 7.49274, -0.25763, 4.54077, 0.80658, -4.65707, 0.51416, -5.31024, -0.45648, -3.62238, 0.87518, -3.54469], "curve": [0.508, 0, 0.725, 1]}, {"time": 0.8333, "offset": 14, "vertices": [-0.98553, -1.7793, -0.28058, -2.01456, -0.45648, -3.62238, 0.87518, -3.54469], "curve": [0.933, 0, 1.133, 1]}, {"time": 1.2333, "offset": 6, "vertices": [-2.66248, 4.10059, -4.60565, 11.58276, -7.10693, 7.49274, -0.25763, 4.54077, 0.80658, -4.65707, 0.51416, -5.31024, -0.45648, -3.62238, 0.87518, -3.54469], "curve": [1.342, 0, 1.558, 1]}, {"time": 1.6667, "offset": 14, "vertices": [-0.98553, -1.7793, -0.28058, -2.01456, -0.45648, -3.62238, 0.87518, -3.54469]}]}}, "hoodmaozi-bei2": {"hoodmaozi-bei": {"deform": [{"curve": [0.208, 0, 0.625, 1]}, {"time": 0.8333, "offset": 16, "vertices": [-28.14294, -3.9543, -28.14294, -3.9543], "curve": [1.042, 0, 1.458, 1]}, {"time": 1.6667}]}}, "hoodmaozi-zuoqian2": {"hoodmaozi-zuoqian": {"deform": [{"vertices": [1.9627, -8.46444, 2.01079, -8.4504, 0.02693, -0.29247, 0.26397, -0.33916, 0, 0, 0, 0, -7.83524, -4.84269, -8.24599, -3.82526, 0, 0, -5.48661, -4.94846, -5.99399, -5.77803, 1.05241, 1.89839, 1.55078, 2.47574, -1.1903, -7.68844, 6.36629, -11.1748, 6.56588, -11.16393], "curve": [0.182, 0.44, 0.378, 1]}, {"time": 0.5, "vertices": [0.91803, -3.91786, 1.146, -3.85736, -0.76672, -18.77792, -2.67084, -18.60271, 0, 0, 0, 0, -8.02106, -14.27799, -9.4306, -13.38909, 0, 0, -5.4873, -5.57561, -5.44916, -6.26089, 3.36206, -4.24874, -4.40851, 4.2657, 4.5863, -8.05472, 1.54492, -11.00357, 2.18695, -10.8942], "curve": [0.708, 0, 1.125, 1]}, {"time": 1.3333, "vertices": [2.57062, -11.11018, 2.51404, -11.12318, 0.48877, 10.4646, 1.9718, 10.28878, 0, 0, 0, 0, -7.72711, 0.6479, -7.55664, 1.74014, 0, 0, -5.48621, -4.5835, -6.31104, -5.49704, -0.29163, 5.47552, -1.93097, 0.17159, -1.54736, -0.02598, 9.17194, -11.27444, 9.11407, -11.32089], "curve": [1.419, 0, 1.54, 0.45]}, {"time": 1.6667, "vertices": [1.9627, -8.46444, 2.01079, -8.4504, 0.02693, -0.29247, 0.26397, -0.33916, 0, 0, 0, 0, -7.83524, -4.84269, -8.24599, -3.82526, 0, 0, -5.48661, -4.94846, -5.99399, -5.77803, 1.05241, 1.89839, 1.55078, 2.47574, -1.1903, -7.68844, 6.36629, -11.1748, 6.56588, -11.16393]}]}}}}}, "wolf_win": {"slots": {"bone3": {"attachment": [{}]}, "bone4": {"attachment": [{}]}, "bone5": {"attachment": [{}]}, "bone6": {"attachment": [{}]}, "bone7": {"attachment": [{}]}, "bone8": {"attachment": [{}]}, "bone31": {"attachment": [{}]}, "bone32": {"attachment": [{}]}, "bone33": {"attachment": [{}]}, "bone50": {"attachment": [{}]}, "bone156": {"rgba": [{"color": "ffffff3a", "curve": [0.125, 1, 0.375, 1, 0.125, 1, 0.375, 1, 0.125, 1, 0.375, 1, 0.125, 0.23, 0.375, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.625, 1, 0.875, 1, 0.625, 1, 0.875, 1, 0.625, 1, 0.875, 1, 0.625, 1, 0.875, 0.23]}, {"time": 1, "color": "ffffff3a"}]}, "fabei2": {"attachment": [{}]}, "head hood back2": {"attachment": [{}]}, "hoodbi-youbei2": {"attachment": [{}]}, "hoodbi-youqian2": {"attachment": [{}]}, "hoodbi-zuo2": {"attachment": [{}]}, "hooddoupeng-bei2": {"attachment": [{}]}, "hooddoupeng-qian2": {"attachment": [{}]}, "hooddoupeng-youbei2": {"attachment": [{}]}, "hoodlanzi-di2": {"attachment": [{}]}, "hoodmaozi-bei2": {"attachment": [{}]}, "hoodmaozi-you2": {"attachment": [{}]}, "hoodmaozi-zuoqian2": {"attachment": [{}]}, "hoodmianbao2": {"attachment": [{}]}, "hoodshenti2": {"attachment": [{}]}, "hoodshouzhang-zuo3": {"attachment": [{}]}, "hoodtoufa-qian2": {"attachment": [{}]}, "hoodtoufa-shang2": {"attachment": [{}]}, "hoodtoufa-youbei2": {"attachment": [{}]}, "hoodtoufa-zuobei2": {"attachment": [{}]}, "hoodtuzier-you2": {"attachment": [{}]}, "hoodtuzier-zuo2": {"attachment": [{}]}, "hoodtuzitou2": {"attachment": [{}]}, "hoodzui2": {"attachment": [{}]}, "liuhai2": {"attachment": [{}]}, "sedai-qian2": {"attachment": [{}]}, "texiao-guang2": {"rgba": [{"color": "ffffffc7", "curve": [0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 0.9, 0.148, 1]}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff66", "curve": [0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 0.4, 0.903, 0.61]}, {"time": 1, "color": "ffffffc7"}]}, "texiaoixiantiaoguang2": {"rgba": [{"color": "ffffff5d", "curve": [0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 1, 0.148, 1, 0.076, 0.16, 0.148, 0]}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": [0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 1, 0.903, 1, 0.799, 1, 0.903, 0.64]}, {"time": 1, "color": "ffffff5d"}]}, "texiaoixiantiaoguang3": {"rgba": [{"color": "ffffffdd", "curve": [0.128, 1, 0.303, 1, 0.128, 1, 0.303, 1, 0.128, 1, 0.303, 1, 0.128, 0.62, 0.303, 0]}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": [0.952, 1, 0.975, 1, 0.952, 1, 0.975, 1, 0.952, 1, 0.975, 1, 0.952, 1, 0.975, 0.95]}, {"time": 1, "color": "ffffffdd"}]}}, "bones": {"bone154": {"rotate": [{"value": -1.68, "curve": [0.213, -0.82, 0.505, 1.3]}, {"time": 0.6667, "value": 1.3, "curve": [0.753, 1.3, 0.873, 0.73]}, {"time": 1, "value": 0.04}], "translate": [{"x": -6.8, "y": -3.98, "curve": [0.037, 0.14, 0.38, 8.97, 0.037, 0.59, 0.38, 6.4]}, {"time": 0.6667, "x": 8.97, "y": 6.4, "curve": [0.753, 8.97, 0.873, 5.97, 0.753, 6.4, 0.873, 4.42]}, {"time": 1, "x": 2.3, "y": 2.01}], "scale": [{"x": 1.018, "y": 1.018, "curve": [0.213, 1.008, 0.505, 0.984, 0.213, 1.008, 0.505, 0.984]}, {"time": 0.6667, "x": 0.984, "y": 0.984, "curve": [0.753, 0.984, 0.873, 0.991, 0.753, 0.984, 0.873, 0.991]}, {"time": 1, "x": 0.999, "y": 0.999}]}, "bone": {"translate": [{"x": -1763.85}]}, "bone150": {"translate": [{"x": -2250.78, "curve": [0.015, -1290.9, 0.152, -69.24, 0.015, 0, 0.152, 0]}, {"time": 0.2667, "x": -69.24, "curve": [0.391, -54.7, 0.817, 0, 0.391, 0, 0.817, 0]}, {"time": 1}], "scale": [{"x": 1.088, "curve": [0.018, 1.088, 0.042, 1.291, 0.018, 1, 0.042, 1.001]}, {"time": 0.0667, "x": 1.559, "y": 1.002, "curve": [0.136, 1.327, 0.21, 0.98, 0.136, 1.005, 0.21, 1.01]}, {"time": 0.2333, "x": 0.98, "y": 1.01, "curve": [0.342, 0.98, 0.558, 1, 0.342, 1.01, 0.558, 1]}, {"time": 0.6667}]}, "bone152": {"translate": [{"y": -28.63, "curve": [0.009, 0, 0.095, 0, 0.009, -30.51, 0.095, -32.91]}, {"time": 0.1667, "y": -32.91, "curve": [0.375, 0, 0.792, 0, 0.375, -32.91, 0.792, 0]}, {"time": 1}]}, "bone153": {"rotate": [{"value": -0.15, "curve": [0.173, 0.4, 0.351, 0.99]}, {"time": 0.4667, "value": 0.99, "curve": [0.596, 0.99, 0.813, 0.1]}, {"time": 1, "value": -0.49}], "translate": [{"x": 0.66, "y": -1.13, "curve": [0.026, -1.88, 0.266, -5.11, 0.026, 3.23, 0.266, 8.78]}, {"time": 0.4667, "x": -5.11, "y": 8.78, "curve": [0.596, -5.11, 0.813, -0.61, 0.596, 8.78, 0.813, 1.05]}, {"time": 1, "x": 2.39, "y": -4.11}]}, "bone159": {"rotate": [{"value": 5.13}, {"time": 1}]}, "bone161": {"rotate": [{"value": 6.02, "curve": [0.061, 7.98, 0.2, 12.33]}, {"time": 0.2667, "value": 12.33, "curve": [0.333, 12.33, 0.467, -2.25]}, {"time": 0.5333, "value": -2.25, "curve": [0.65, -2.25, 0.921, -0.56]}, {"time": 1}]}, "bone162": {"rotate": [{"value": 25.86}]}, "bone164": {"rotate": [{"value": -2.47, "curve": [0.063, -2.7, 0.119, -2.84]}, {"time": 0.1667, "value": -2.84, "curve": [0.375, -2.84, 0.792, 0]}, {"time": 1}]}, "bone166": {"rotate": [{"value": -0.79, "curve": [0.185, -4.82, 0.377, -9.36]}, {"time": 0.5, "value": -9.36, "curve": [0.622, -9.36, 0.823, -3.2]}, {"time": 1, "value": 1.08}]}, "bone167": {"rotate": [{"value": 9.46, "curve": [0.213, 3.96, 0.505, -9.52]}, {"time": 0.6667, "value": -9.52, "curve": [0.753, -9.52, 0.873, -5.91]}, {"time": 1, "value": -1.5}]}, "bone168": {"rotate": [{"value": -11.59, "curve": [0.063, -8.79, 0.119, -7.07]}, {"time": 0.1667, "value": -7.07, "curve": [0.375, -7.07, 0.792, 0]}, {"time": 1}], "translate": [{"x": 0.66, "y": -30.64, "curve": [0.056, 0.37, 0.57, 0, 0.056, -17.16, 0.57, 0]}, {"time": 1}]}, "bone169": {"rotate": [{"value": -7.23, "curve": [0.063, -7.91, 0.119, -8.32]}, {"time": 0.1667, "value": -8.32, "curve": [0.375, -8.32, 0.792, 0]}, {"time": 1}]}, "bone170": {"rotate": [{"value": 2.08, "curve": [0.185, -1.94, 0.377, -6.48]}, {"time": 0.5, "value": -6.48, "curve": [0.622, -6.48, 0.823, -0.33]}, {"time": 1, "value": 3.95}]}, "bone171": {"rotate": [{"value": 10.01, "curve": [0.213, 4.48, 0.505, -9.06]}, {"time": 0.6667, "value": -9.06, "curve": [0.753, -9.06, 0.873, -5.43]}, {"time": 1, "value": -1}], "translate": [{"x": 2.96, "y": -1.41, "curve": [0.037, 8.03, 0.38, 14.48, 0.037, -3.25, 0.38, -5.6]}, {"time": 0.6667, "x": 14.48, "y": -5.6, "curve": [0.753, 14.48, 0.873, 12.29, 0.753, -5.6, 0.873, -4.8]}, {"time": 1, "x": 9.61, "y": -3.83}]}, "bone172": {"rotate": [{"value": 11.67, "curve": [0.063, 12.76, 0.119, 13.42]}, {"time": 0.1667, "value": 13.42, "curve": [0.375, 13.42, 0.792, 0]}, {"time": 1}], "translate": [{"x": -0.54, "y": 6.42, "curve": [0.009, -0.58, 0.095, -0.63, 0.009, 6.84, 0.095, 7.38]}, {"time": 0.1667, "x": -0.63, "y": 7.38, "curve": [0.375, -0.63, 0.792, 0, 0.375, 7.38, 0.792, 0]}, {"time": 1}]}, "bone173": {"rotate": [{"value": 10.42, "curve": [0.102, 12.2, 0.194, 13.43]}, {"time": 0.2667, "value": 13.43, "curve": [0.45, 13.43, 0.817, 0]}, {"time": 1}]}, "bone174": {"rotate": [{"value": -5.39, "curve": [0.208, -5.39, 0.625, 2.15]}, {"time": 0.8333, "value": 2.15, "curve": [0.881, 2.15, 0.938, 1.78]}, {"time": 1, "value": 1.17}]}, "bone176": {"rotate": [{"value": 1.25, "curve": [0.127, -0.85, 0.247, -2.56]}, {"time": 0.3333, "value": -2.56, "curve": [0.495, -2.56, 0.787, 3.84]}, {"time": 1, "value": 6.46}]}, "bone178": {"rotate": [{"value": 1.32, "curve": [0.182, -1.38, 0.378, -4.82]}, {"time": 0.5, "value": -4.82, "curve": [0.623, -4.82, 0.819, -1.38]}, {"time": 1, "value": 1.32}]}, "bone180": {"rotate": [{"value": 7.12, "curve": [0.208, 7.12, 0.625, -4.43]}, {"time": 0.8333, "value": -4.43, "curve": [0.881, -4.43, 0.938, -3.86]}, {"time": 1, "value": -2.93}]}, "bone182": {"rotate": [{"value": -0.88}, {"time": 0.7667, "value": -11.02, "curve": [0.83, -11.02, 0.911, -10.04]}, {"time": 1, "value": -8.62}]}, "bone184": {"rotate": [{"value": 3.9, "curve": [0.101, 6.36, 0.2, 8.73]}, {"time": 0.2667, "value": 8.73, "curve": [0.537, -0.66, 0.786, -6.98]}, {"time": 1, "value": -8.35}]}, "bone186": {"rotate": [{"value": 8.99, "curve": [0.063, 9.83, 0.119, 10.34]}, {"time": 0.1667, "value": 10.34, "curve": [0.375, 10.34, 0.792, 0]}, {"time": 1}]}, "bone188": {"rotate": [{"value": -7.05, "curve": [0.208, -1.02, 0.454, 8.81]}, {"time": 0.6, "value": 8.81, "curve": [0.7, 8.81, 0.85, 3.51]}, {"time": 1, "value": -1.79}]}, "bone190": {"rotate": [{"value": 2.65}, {"time": 0.6667, "value": 13.25, "curve": [0.753, 13.25, 0.873, 11.05]}, {"time": 1, "value": 8.37}]}, "bone192": {"rotate": [{"value": -6.28, "curve": [0.063, -7.88, 0.125, -9.48]}, {"time": 0.1667, "value": -9.48, "curve": [0.375, -9.48, 0.792, 7.94]}, {"time": 1, "value": 7.94}]}, "bone194": {"rotate": [{"value": -10.29, "curve": [0.063, -11.24, 0.119, -11.83]}, {"time": 0.1667, "value": -11.83, "curve": [0.375, -11.83, 0.792, 0]}, {"time": 1}]}, "bone196": {"rotate": [{"value": -0.13, "curve": [0.182, -6.29, 0.378, -14.12]}, {"time": 0.5, "value": -14.12, "curve": [0.623, -14.12, 0.819, -6.29]}, {"time": 1, "value": -0.13}]}, "bone197": {"rotate": [{"value": 0.18, "curve": [0.182, -2.56, 0.378, -6.04]}, {"time": 0.5, "value": -6.04, "curve": [0.623, -6.04, 0.819, -2.56]}, {"time": 1, "value": 0.18}], "translate": [{"x": 4.13, "y": 2.71, "curve": [0.028, -2.24, 0.285, -10.34, 0.028, 1.32, 0.285, -0.44]}, {"time": 0.5, "x": -10.34, "y": -0.44, "curve": [0.623, -10.34, 0.819, -2.24, 0.623, -0.44, 0.819, 1.32]}, {"time": 1, "x": 4.13, "y": 2.71}]}, "bone198": {"rotate": [{"value": 8.14, "curve": [0.186, 2.44, 0.43, -10.24]}, {"time": 0.5667, "value": -10.24, "curve": [0.676, -10.24, 0.836, -5.48]}, {"time": 1, "value": -0.33}]}, "bone200": {"rotate": [{"value": 3.07, "curve": [0.127, 9.1, 0.247, 14.03]}, {"time": 0.3333, "value": 14.03, "curve": [0.495, 14.03, 0.787, -4.37]}, {"time": 1, "value": -11.88}]}, "bone202": {"rotate": [{"value": 8.94, "curve": [0.213, 4.59, 0.505, -6.05]}, {"time": 0.6667, "value": -6.05, "curve": [0.753, -6.05, 0.873, -3.2]}, {"time": 1, "value": 0.29}]}, "bone204": {"rotate": [{"value": 6.12, "curve": [0.127, 12.89, 0.247, 18.43]}, {"time": 0.3333, "value": 18.43, "curve": [0.495, 18.43, 0.787, -2.25]}, {"time": 1, "value": -10.69}], "translate": [{"x": 9.97, "y": -0.85, "curve": [0.019, 14.48, 0.19, 20.22, 0.019, -1.46, 0.19, -2.23]}, {"time": 0.3333, "x": 20.22, "y": -2.23, "curve": [0.495, 20.22, 0.787, 3, 0.495, -2.23, 0.787, 0.1]}, {"time": 1, "x": -4.03, "y": 1.05}]}, "bone206": {"rotate": [{"value": -31.66, "curve": [0.213, -23.08, 0.505, -2.09]}, {"time": 0.6667, "value": -2.09, "curve": [0.753, -2.09, 0.873, -7.72]}, {"time": 1, "value": -14.6}]}, "bone207": {"rotate": [{"value": -20.99, "curve": [0.216, -16.39, 0.578, 11.85]}, {"time": 0.7667, "value": 11.85, "curve": [0.83, 11.85, 0.911, 8.84]}, {"time": 1, "value": 4.52}]}, "bone208": {"rotate": [{"value": -1.48, "curve": [0.117, -3.04, 0.252, -5.38]}, {"time": 0.3333, "value": -5.38, "curve": [0.577, -1.16, 0.802, 1.76]}, {"time": 1, "value": 2.73}]}, "bone210": {"rotate": [{"value": -1.61}, {"time": 0.6667, "value": -8.03, "curve": [0.782, -6.17, 0.894, -4.6]}, {"time": 1, "value": -3.39}]}, "bone212": {"rotate": [{"value": 3.16, "curve": [0.052, 3.91, 0.108, 4.98]}, {"time": 0.1667, "value": 6.29, "curve": [0.375, 6.29, 0.792, -5.97]}, {"time": 1, "value": -5.97}]}, "bone214": {"rotate": [{"value": 1.45}, {"time": 0.6667, "value": 7.25, "curve": [0.753, 7.25, 0.873, 6.05]}, {"time": 1, "value": 4.59}]}, "bone216": {"translate": [{"x": -37.66, "curve": [0.017, -25.23, 0.171, -9.41, 0.017, 0, 0.171, 0]}, {"time": 0.3, "x": -9.41, "curve": [0.348, -4.14, 0.825, 0, 0.348, 0, 0.825, 0]}, {"time": 1}]}, "bone217": {"translate": [{"x": -54.95, "y": 1.05, "curve": [0.019, -35.24, 0.19, -10.15, 0.019, 0.67, 0.19, 0.19]}, {"time": 0.3333, "x": -10.15, "y": 0.19, "curve": [0.435, -4.36, 0.833, 0, 0.435, 0.08, 0.833, 0]}, {"time": 1}]}, "bone219": {"translate": [{"x": 59.47, "y": -1.86, "curve": [0.015, 38.65, 0.152, 12.15, 0.015, -1.21, 0.152, -0.38]}, {"time": 0.2667, "x": 12.15, "y": -0.38, "curve": [0.432, 8.63, 0.817, 0, 0.432, -0.27, 0.817, 0]}, {"time": 1}]}, "bone221": {"translate": [{"x": 34.37, "y": -0.73, "curve": [0.022, 28.91, 0.228, 21.96, 0.022, -0.62, 0.228, -0.47]}, {"time": 0.4, "x": 21.96, "y": -0.47, "curve": [0.535, 21.13, 0.85, 18.75, 0.535, -0.45, 0.85, -0.4]}, {"time": 1, "x": 18.75, "y": -0.4}]}}, "attachments": {"default": {"bone161": {"langbizi": {"deform": [{"curve": [0.042, 0, 0.125, 1]}, {"time": 0.1667, "vertices": [3.81985, 0.67062, -1.74161, -1.2906, -6.90268, 5.67783, 5.39423, 2.91594, 3.98804, -0.28442, 2.41315, -2.52932, -1.69049, -7.18735, -13.81966, -5.38021, 0.67116, -3.81964, -29.58368, -2.24333], "curve": [0.375, 0, 0.792, 1]}, {"time": 1}]}}, "langertou2": {"langertou": {"deform": [{"offset": 4, "vertices": [0.52784, -0.46271, 0.68312, -0.16175, -0.49205, 0.50062, -0.34699, -1.07747, 0.42601, 1.04872, 2.42624, -3.84515, -2.08928, 5.01783, -1.87694, -5.06317, -3.98516, -3.75223, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.40217, -2.31357, -2.49764, 5.33369, 6.08299, -3.95559, 0, 0, 0, 0, 0, 0, -0.46274, -0.52792, 0.50066, 0.4921, -5.0211, -6.62474, 5.60843, 6.12813, -3.36039, -6.02275, 3.90126, 5.68587], "curve": [0.167, 0.33, 0.379, 1]}, {"time": 0.5, "offset": 4, "vertices": [2.86145, -2.50836, 3.70325, -0.87685, -2.66742, 2.71387, -1.88104, -5.841, 2.30945, 5.68518, 2.83899, -6.92133, -2.82428, 1.16147, 1.06772, -2.46704, -0.07487, -3.40375, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.19318, -4.44424, -3.3967, 8.72803, 11.21497, -9.53003, 0, 0, 0, 0, 0, 0, -2.50854, -2.86188, 2.71411, 2.66772, -6.20343, -10.04556, 7.04144, 9.45169, -4.6709, -8.83615, 5.41699, 8.3942], "curve": [0.625, 0, 0.875, 1]}, {"time": 1}]}}, "langshenti2": {"langshenti": {"deform": [{"vertices": [-148.55353, -86.45374, -116.91599, 125.98859, 171.87897, 0, -148.55353, -86.45374, -116.91599, 125.98859, 171.87897]}]}}, "langshou-qian2": {"langshou-qian": {"deform": [{"offset": 10, "vertices": [-26.91905, 39.74959, -37.70757, 71.64271, 7.17463, 20.82506, 12.31265, -17.06887], "curve": [0.063, 0.62, 0.119, 1]}, {"time": 0.1667, "offset": 10, "vertices": [-30.94489, 45.69427, -43.34686, 82.35712, 8.24762, 23.93951, 14.15405, -19.62158], "curve": [0.375, 0, 0.792, 1]}, {"time": 1}]}}}}}, "wolf_win_idle": {"slots": {"bone3": {"attachment": [{}]}, "bone4": {"attachment": [{}]}, "bone5": {"attachment": [{}]}, "bone6": {"attachment": [{}]}, "bone7": {"attachment": [{}]}, "bone8": {"attachment": [{}]}, "bone31": {"attachment": [{}]}, "bone32": {"attachment": [{}]}, "bone33": {"attachment": [{}]}, "bone50": {"attachment": [{}]}, "bone156": {"rgba": [{"color": "ffffff3a", "curve": [0.1, 1, 0.3, 1, 0.1, 1, 0.3, 1, 0.1, 1, 0.3, 1, 0.1, 0.23, 0.3, 1]}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff", "curve": [1.292, 1, 1.542, 1, 1.292, 1, 1.542, 1, 1.292, 1, 1.542, 1, 1.292, 1, 1.542, 0.23]}, {"time": 1.6667, "color": "ffffff3a"}]}, "fabei2": {"attachment": [{}]}, "head hood back2": {"attachment": [{}]}, "hoodbi-youbei2": {"attachment": [{}]}, "hoodbi-youqian2": {"attachment": [{}]}, "hoodbi-zuo2": {"attachment": [{}]}, "hooddoupeng-bei2": {"attachment": [{}]}, "hooddoupeng-qian2": {"attachment": [{}]}, "hooddoupeng-youbei2": {"attachment": [{}]}, "hoodlanzi-di2": {"attachment": [{}]}, "hoodmaozi-bei2": {"attachment": [{}]}, "hoodmaozi-you2": {"attachment": [{}]}, "hoodmaozi-zuoqian2": {"attachment": [{}]}, "hoodmianbao2": {"attachment": [{}]}, "hoodshenti2": {"attachment": [{}]}, "hoodshouzhang-zuo3": {"attachment": [{}]}, "hoodtoufa-qian2": {"attachment": [{}]}, "hoodtoufa-shang2": {"attachment": [{}]}, "hoodtoufa-youbei2": {"attachment": [{}]}, "hoodtoufa-zuobei2": {"attachment": [{}]}, "hoodtuzier-you2": {"attachment": [{}]}, "hoodtuzier-zuo2": {"attachment": [{}]}, "hoodtuzitou2": {"attachment": [{}]}, "hoodzui2": {"attachment": [{}]}, "liuhai2": {"attachment": [{}]}, "sedai-qian2": {"attachment": [{}]}, "texiao-guang2": {"rgba": [{"color": "ffffffc7", "curve": [0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 0.9, 0.247, 1]}, {"time": 0.3333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff66", "curve": [1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 0.4, 1.485, 0.61]}, {"time": 1.6667, "color": "ffffffc7"}]}, "texiaoixiantiaoguang2": {"rgba": [{"color": "ffffff5d", "curve": [0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 1, 0.247, 1, 0.127, 0.16, 0.247, 0]}, {"time": 0.3333, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": [1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 1, 1.485, 1, 1.289, 1, 1.485, 0.64]}, {"time": 1.6667, "color": "ffffff5d"}]}, "texiaoixiantiaoguang3": {"rgba": [{"color": "ffffffdd", "curve": [0.213, 1, 0.505, 1, 0.213, 1, 0.505, 1, 0.213, 1, 0.505, 1, 0.213, 0.62, 0.505, 0]}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": [1.547, 1, 1.604, 1, 1.547, 1, 1.604, 1, 1.547, 1, 1.604, 1, 1.547, 1, 1.604, 0.95]}, {"time": 1.6667, "color": "ffffffdd"}]}}, "bones": {"bone154": {"rotate": [{"value": 0.04, "curve": [0.182, -0.91, 0.378, -2.13]}, {"time": 0.5, "value": -2.13, "curve": [0.708, -2.13, 1.125, 1.3]}, {"time": 1.3333, "value": 1.3, "curve": [1.419, 1.3, 1.54, 0.73]}, {"time": 1.6667, "value": 0.04}], "translate": [{"x": 2.3, "y": 2.01, "curve": [0.182, -2.74, 0.378, -9.15, 0.182, -1.31, 0.378, -5.53]}, {"time": 0.5, "x": -9.15, "y": -5.53, "curve": [0.708, -9.15, 1.125, 8.97, 0.708, -5.53, 1.125, 6.4]}, {"time": 1.3333, "x": 8.97, "y": 6.4, "curve": [1.419, 8.97, 1.54, 5.97, 1.419, 6.4, 1.54, 4.42]}, {"time": 1.6667, "x": 2.3, "y": 2.01}], "scale": [{"x": 0.999, "y": 0.999, "curve": [0.182, 1.01, 0.378, 1.023, 0.182, 1.01, 0.378, 1.023]}, {"time": 0.5, "x": 1.023, "y": 1.023, "curve": [0.708, 1.023, 1.125, 0.984, 0.708, 1.023, 1.125, 0.984]}, {"time": 1.3333, "x": 0.984, "y": 0.984, "curve": [1.419, 0.984, 1.54, 0.991, 1.419, 0.984, 1.54, 0.991]}, {"time": 1.6667, "x": 0.999, "y": 0.999}]}, "bone": {"translate": [{"x": -1763.85}]}, "bone152": {"translate": [{"curve": [0.208, 0, 0.625, 0, 0.208, 0, 0.625, -32.91]}, {"time": 0.8333, "y": -32.91, "curve": [1.042, 0, 1.458, 0, 1.042, -32.91, 1.458, 0]}, {"time": 1.6667}]}, "bone153": {"rotate": [{"value": -0.49, "curve": [0.102, -0.83, 0.196, -1.08]}, {"time": 0.2667, "value": -1.08, "curve": [0.483, -1.08, 0.917, 0.99]}, {"time": 1.1333, "value": 0.99, "curve": [1.263, 0.99, 1.479, 0.1]}, {"time": 1.6667, "value": -0.49}], "translate": [{"x": 2.39, "y": -4.11, "curve": [0.102, 4.08, 0.196, 5.36, 0.102, -7.02, 0.196, -9.22]}, {"time": 0.2667, "x": 5.36, "y": -9.22, "curve": [0.483, 5.36, 0.917, -5.11, 0.483, -9.22, 0.917, 8.78]}, {"time": 1.1333, "x": -5.11, "y": 8.78, "curve": [1.263, -5.11, 1.479, -0.61, 1.263, 8.78, 1.479, 1.05]}, {"time": 1.6667, "x": 2.39, "y": -4.11}]}, "bone159": {"rotate": [{"curve": [0.208, 0, 0.625, -2.23]}, {"time": 0.8333, "value": -2.23, "curve": [1.042, -2.23, 1.458, 0]}, {"time": 1.6667}]}, "bone161": {"rotate": [{"value": -0.08, "curve": [0.125, 2.84, 0.25, 5.76]}, {"time": 0.3333, "value": 5.76, "curve": [0.583, 5.76, 1.083, -5.92]}, {"time": 1.3333, "value": -5.92, "curve": [1.417, -5.92, 1.542, -3]}, {"time": 1.6667, "value": -0.08}]}, "bone162": {"rotate": [{"value": -4.28, "curve": [0.167, 2.35, 0.379, 15.81]}, {"time": 0.5, "value": 15.81, "curve": [0.75, 15.81, 1.25, -8.83]}, {"time": 1.5, "value": -8.83, "curve": [1.546, -8.83, 1.604, -7.01]}, {"time": 1.6667, "value": -4.28}]}, "bone164": {"rotate": [{"curve": [0.208, 0, 0.625, -2.84]}, {"time": 0.8333, "value": -2.84, "curve": [1.042, -2.84, 1.458, 0]}, {"time": 1.6667}]}, "bone166": {"rotate": [{"value": 1.08, "curve": [0.102, 3.65, 0.197, 5.59]}, {"time": 0.2667, "value": 5.59, "curve": [0.492, 5.59, 0.942, -9.36]}, {"time": 1.1667, "value": -9.36, "curve": [1.289, -9.36, 1.49, -3.2]}, {"time": 1.6667, "value": 1.08}]}, "bone167": {"rotate": [{"value": -1.5, "curve": [0.182, 4.57, 0.378, 12.3]}, {"time": 0.5, "value": 12.3, "curve": [0.708, 12.3, 1.125, -9.52]}, {"time": 1.3333, "value": -9.52, "curve": [1.419, -9.52, 1.54, -5.91]}, {"time": 1.6667, "value": -1.5}]}, "bone168": {"rotate": [{"curve": [0.208, 0, 0.625, -7.07]}, {"time": 0.8333, "value": -7.07, "curve": [1.042, -7.07, 1.458, 0]}, {"time": 1.6667}]}, "bone169": {"rotate": [{"curve": [0.208, 0, 0.625, -8.32]}, {"time": 0.8333, "value": -8.32, "curve": [1.042, -8.32, 1.458, 0]}, {"time": 1.6667}]}, "bone170": {"rotate": [{"value": 3.95, "curve": [0.102, 6.52, 0.197, 8.46]}, {"time": 0.2667, "value": 8.46, "curve": [0.492, 8.46, 0.942, -6.48]}, {"time": 1.1667, "value": -6.48, "curve": [1.289, -6.48, 1.49, -0.33]}, {"time": 1.6667, "value": 3.95}]}, "bone171": {"rotate": [{"value": -1, "curve": [0.182, 5.1, 0.378, 12.86]}, {"time": 0.5, "value": 12.86, "curve": [0.708, 12.86, 1.125, -9.06]}, {"time": 1.3333, "value": -9.06, "curve": [1.419, -9.06, 1.54, -5.43]}, {"time": 1.6667, "value": -1}], "translate": [{"x": 9.61, "y": -3.83, "curve": [0.182, 5.93, 0.378, 1.24, 0.182, -2.49, 0.378, -0.78]}, {"time": 0.5, "x": 1.24, "y": -0.78, "curve": [0.708, 1.24, 1.125, 14.48, 0.708, -0.78, 1.125, -5.6]}, {"time": 1.3333, "x": 14.48, "y": -5.6, "curve": [1.419, 14.48, 1.54, 12.29, 1.419, -5.6, 1.54, -4.8]}, {"time": 1.6667, "x": 9.61, "y": -3.83}]}, "bone172": {"rotate": [{"curve": [0.208, 0, 0.625, 13.42]}, {"time": 0.8333, "value": 13.42, "curve": [1.042, 13.42, 1.458, 0]}, {"time": 1.6667}], "translate": [{"curve": [0.208, 0, 0.625, -0.63, 0.208, 0, 0.625, 7.38]}, {"time": 0.8333, "x": -0.63, "y": 7.38, "curve": [1.042, -0.63, 1.458, 0, 1.042, 7.38, 1.458, 0]}, {"time": 1.6667}]}, "bone173": {"rotate": [{"curve": [0.233, 0, 0.7, 13.43]}, {"time": 0.9333, "value": 13.43, "curve": [1.117, 13.43, 1.483, 0]}, {"time": 1.6667}]}, "bone174": {"rotate": [{"value": 1.17, "curve": [0.213, -0.73, 0.505, -5.39]}, {"time": 0.6667, "value": -5.39, "curve": [0.875, -5.39, 1.292, 2.15]}, {"time": 1.5, "value": 2.15, "curve": [1.547, 2.15, 1.604, 1.78]}, {"time": 1.6667, "value": 1.17}]}, "bone176": {"rotate": [{"value": 6.46, "curve": [0.063, 7.3, 0.119, 7.81]}, {"time": 0.1667, "value": 7.81, "curve": [0.375, 7.81, 0.792, -2.56]}, {"time": 1, "value": -2.56, "curve": [1.162, -2.56, 1.453, 3.84]}, {"time": 1.6667, "value": 6.46}]}, "bone178": {"rotate": [{"value": 1.32, "curve": [0.127, 3.29, 0.247, 4.9]}, {"time": 0.3333, "value": 4.9, "curve": [0.542, 4.9, 0.958, -4.82]}, {"time": 1.1667, "value": -4.82, "curve": [1.289, -4.82, 1.485, -1.38]}, {"time": 1.6667, "value": 1.32}]}, "bone180": {"rotate": [{"value": -2.93, "curve": [0.213, -0.02, 0.505, 7.12]}, {"time": 0.6667, "value": 7.12, "curve": [0.875, 7.12, 1.292, -4.43]}, {"time": 1.5, "value": -4.43, "curve": [1.547, -4.43, 1.604, -3.86]}, {"time": 1.6667, "value": -2.93}]}, "bone182": {"rotate": [{"value": -8.62, "curve": [0.204, -5.52, 0.454, 0]}, {"time": 0.6}, {"time": 1.4333, "value": -11.02, "curve": [1.497, -11.02, 1.578, -10.04]}, {"time": 1.6667, "value": -8.62}]}, "bone184": {"rotate": [{"value": -8.35, "curve": [0.034, -8.63, 0.067, -8.77]}, {"time": 0.1, "value": -8.77, "curve": [0.225, -8.77, 0.475, 2.3]}, {"time": 0.6, "value": 2.3, "curve": [0.721, 5.13, 0.852, 8.73]}, {"time": 0.9333, "value": 8.73, "curve": [1.204, -0.66, 1.453, -6.98]}, {"time": 1.6667, "value": -8.35}]}, "bone186": {"rotate": [{"curve": [0.208, 0, 0.625, 10.34]}, {"time": 0.8333, "value": 10.34, "curve": [1.042, 10.34, 1.458, 0]}, {"time": 1.6667}]}, "bone188": {"rotate": [{"value": -1.79, "curve": [0.15, -7.09, 0.3, -12.39]}, {"time": 0.4, "value": -12.39, "curve": [0.617, -12.39, 1.05, 8.81]}, {"time": 1.2667, "value": 8.81, "curve": [1.367, 8.81, 1.517, 3.51]}, {"time": 1.6667, "value": -1.79}]}, "bone190": {"rotate": [{"value": 8.37, "curve": [0.182, 4.69, 0.378, 0]}, {"time": 0.5}, {"time": 1.3333, "value": 13.25, "curve": [1.419, 13.25, 1.54, 11.05]}, {"time": 1.6667, "value": 8.37}]}, "bone192": {"rotate": [{"value": 7.94, "curve": [0.125, 7.94, 0.375, -3.07]}, {"time": 0.5, "value": -3.07, "curve": [0.583, -3.07, 0.75, -9.48]}, {"time": 0.8333, "value": -9.48, "curve": [1.042, -9.48, 1.458, 7.94]}, {"time": 1.6667, "value": 7.94}]}, "bone194": {"rotate": [{"curve": [0.208, 0, 0.625, -11.83]}, {"time": 0.8333, "value": -11.83, "curve": [1.042, -11.83, 1.458, 0]}, {"time": 1.6667}]}, "bone196": {"rotate": [{"value": -0.13, "curve": [0.127, 4.35, 0.247, 8.02]}, {"time": 0.3333, "value": 8.02, "curve": [0.542, 8.02, 0.958, -14.12]}, {"time": 1.1667, "value": -14.12, "curve": [1.289, -14.12, 1.485, -6.29]}, {"time": 1.6667, "value": -0.13}]}, "bone197": {"rotate": [{"value": 0.18, "curve": [0.127, 2.17, 0.247, 3.8]}, {"time": 0.3333, "value": 3.8, "curve": [0.542, 3.8, 0.958, -6.04]}, {"time": 1.1667, "value": -6.04, "curve": [1.289, -6.04, 1.485, -2.56]}, {"time": 1.6667, "value": 0.18}], "translate": [{"x": 4.13, "y": 2.71, "curve": [0.127, 8.76, 0.247, 12.55, 0.127, 3.72, 0.247, 4.54]}, {"time": 0.3333, "x": 12.55, "y": 4.54, "curve": [0.542, 12.55, 0.958, -10.34, 0.542, 4.54, 0.958, -0.44]}, {"time": 1.1667, "x": -10.34, "y": -0.44, "curve": [1.289, -10.34, 1.485, -2.24, 1.289, -0.44, 1.485, 1.32]}, {"time": 1.6667, "x": 4.13, "y": 2.71}]}, "bone198": {"rotate": [{"value": -0.33, "curve": [0.186, 5.41, 0.376, 11.63]}, {"time": 0.5, "value": 11.63, "curve": [0.683, 11.63, 1.05, -10.24]}, {"time": 1.2333, "value": -10.24, "curve": [1.343, -10.24, 1.503, -5.48]}, {"time": 1.6667, "value": -0.33}]}, "bone200": {"rotate": [{"value": -11.88, "curve": [0.063, -14.29, 0.119, -15.76]}, {"time": 0.1667, "value": -15.76, "curve": [0.375, -15.76, 0.792, 14.03]}, {"time": 1, "value": 14.03, "curve": [1.162, 14.03, 1.453, -4.37]}, {"time": 1.6667, "value": -11.88}]}, "bone202": {"rotate": [{"value": 0.29, "curve": [0.182, 5.08, 0.378, 11.18]}, {"time": 0.5, "value": 11.18, "curve": [0.708, 11.18, 1.125, -6.05]}, {"time": 1.3333, "value": -6.05, "curve": [1.419, -6.05, 1.54, -3.2]}, {"time": 1.6667, "value": 0.29}]}, "bone204": {"rotate": [{"value": -10.69, "curve": [0.063, -13.39, 0.119, -15.05]}, {"time": 0.1667, "value": -15.05, "curve": [0.375, -15.05, 0.792, 18.43]}, {"time": 1, "value": 18.43, "curve": [1.162, 18.43, 1.453, -2.25]}, {"time": 1.6667, "value": -10.69}], "translate": [{"x": -4.03, "y": 1.05, "curve": [0.063, -6.27, 0.119, -7.65, 0.063, 1.35, 0.119, 1.54]}, {"time": 0.1667, "x": -7.65, "y": 1.54, "curve": [0.375, -7.65, 0.792, 20.22, 0.375, 1.54, 0.792, -2.23]}, {"time": 1, "x": 20.22, "y": -2.23, "curve": [1.162, 20.22, 1.453, 3, 1.162, -2.23, 1.453, 0.1]}, {"time": 1.6667, "x": -4.03, "y": 1.05}]}, "bone206": {"rotate": [{"value": -14.6, "curve": [0.182, -24.06, 0.378, -36.09]}, {"time": 0.5, "value": -36.09, "curve": [0.708, -36.09, 1.125, -2.09]}, {"time": 1.3333, "value": -2.09, "curve": [1.419, -2.09, 1.54, -7.72]}, {"time": 1.6667, "value": -14.6}]}, "bone207": {"rotate": [{"value": 4.52, "curve": [0.204, -5, 0.454, -21.92]}, {"time": 0.6, "value": -21.92, "curve": [0.808, -21.92, 1.225, 11.85]}, {"time": 1.4333, "value": 11.85, "curve": [1.497, 11.85, 1.578, 8.84]}, {"time": 1.6667, "value": 4.52}]}, "bone208": {"rotate": [{"value": 2.73, "curve": [0.058, 3.07, 0.113, 3.24]}, {"time": 0.1667, "value": 3.24, "curve": [0.25, 3.24, 0.417, 0.07]}, {"time": 0.5, "value": 0.07, "curve": [0.625, 0.07, 0.875, -5.38]}, {"time": 1, "value": -5.38, "curve": [1.243, -1.16, 1.469, 1.76]}, {"time": 1.6667, "value": 2.73}]}, "bone210": {"rotate": [{"value": -3.39, "curve": [0.182, -1.22, 0.35, 0]}, {"time": 0.5}, {"time": 1.3333, "value": -8.03, "curve": [1.449, -6.17, 1.561, -4.6]}, {"time": 1.6667, "value": -3.39}]}, "bone212": {"rotate": [{"value": -5.97, "curve": [0.125, -5.97, 0.375, 1.78]}, {"time": 0.5, "value": 1.78, "curve": [0.595, 1.78, 0.708, 3.49]}, {"time": 0.8333, "value": 6.29, "curve": [1.042, 6.29, 1.458, -5.97]}, {"time": 1.6667, "value": -5.97}]}, "bone214": {"rotate": [{"value": 4.59, "curve": [0.182, 2.57, 0.378, 0]}, {"time": 0.5}, {"time": 1.3333, "value": 7.25, "curve": [1.419, 7.25, 1.54, 6.05]}, {"time": 1.6667, "value": 4.59}]}}, "attachments": {"default": {"bone160": {"langtou": {"deform": [{"offset": 23, "vertices": [-6.28394, -5.78693, 2.4501, -4.82935, 4.02067]}, {"time": 0.7667, "offset": 23, "vertices": [-3.66043, -3.19019, 1.79515, -1.78497, 3.19531, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.45436, -9.14142, 7.19331, -6.47272, -9.20996, -0.71808, 1.92572, 8.89499, 0.54312, -9.37604], "curve": [0.868, 0, 1.015, 0.47]}, {"time": 1.1667, "offset": 23, "vertices": [-7.48242, -6.83105, 3.05304, -4.7558, 5.77576, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.05435, -7.14153, 5.45988, -4.76396, -7.55976, -0.98873, 1.2378, 7.63647, 0.80894, -7.45076], "curve": [1.351, 0.47, 1.543, 1]}, {"time": 1.6667, "offset": 23, "vertices": [-6.28394, -5.78693, 2.4501, -4.82935, 4.02067]}]}}, "bone161": {"langbizi": {"deform": [{"curve": [0.208, 0, 0.625, 1]}, {"time": 0.8333, "vertices": [3.81985, 0.67062, -1.74161, -1.2906, -6.90268, 5.67783, 5.39423, 2.91594, 3.98804, -0.28442, 2.41315, -2.52932, -1.69049, -7.18735, -13.81966, -5.38021, 0.67116, -3.81964, -29.58368, -2.24333], "curve": [1.042, 0, 1.458, 1]}, {"time": 1.6667}]}}, "langertou2": {"langertou": {"deform": [{"curve": [0.192, 0, 0.575, 1]}, {"time": 0.7667, "offset": 52, "vertices": [4.92867, 3.15033, -5.82479, 0.5325, -5.34113, 2.38519, 24.12193, 6.84384, -23.14003, 9.31847, 12.76901, -0.47386, 0, 0, 0, 0, 0, 0, -6.91638, 4.80274, 6.57146, -5.02979, -16.24895, 2.62127, 15.96049, -3.09614, -3.63402, 6.72641, 3.24175, -6.86696], "curve": [0.992, 0, 1.442, 1]}, {"time": 1.6667}]}}, "langshou-qian2": {"langshou-qian": {"deform": [{"curve": [0.208, 0, 0.625, 1]}, {"time": 0.8333, "offset": 10, "vertices": [-30.94489, 45.69427, -43.34686, 82.35712, 8.24762, 23.93951, 14.15405, -19.62158], "curve": [1.042, 0, 1.458, 1]}, {"time": 1.6667}]}}}}}}}